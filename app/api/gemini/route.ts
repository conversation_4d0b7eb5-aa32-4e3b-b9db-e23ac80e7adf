import { GoogleGenAI } from '@google/genai';
import { NextRequest, NextResponse } from 'next/server';

// Initialize the GoogleGenAI client
const genAI = new GoogleGenAI({
  apiKey: process.env.GEMINI_API_KEY!,
});

export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const { query, selectedRecords } = await request.json();

    if (!query || !selectedRecords || selectedRecords.length === 0) {
      return NextResponse.json(
        { error: 'Query and selected records are required' },
        { status: 400 }
      );
    }

    // Format the context from selected records
    const context = selectedRecords.map((record: any, index: number) => {
      return `Record ${index + 1}:
Title: ${record.fields.Title || `Record #${record.fields.ID}`}
Content: ${record.fields["Cleaned Body"]}
Source: ${record.fields["HTML URL"]}
Country: ${record.fields.Country}
Section: ${record.fields["Section ID"]}
Breadcrumb: ${record.fields.Breadcrumb}
---`;
    }).join('\n\n');

    // Create the prompt for Gemini
    const prompt = `You are an AI assistant helping to analyze and answer questions about knowledge base records. 

Context Records:
${context}

User Query: ${query}

Please provide a comprehensive answer based on the provided records. If you reference specific information, mention which record(s) it comes from. Be accurate and cite your sources from the provided context.`;

    // Generate content using Gemini 2.0 Flash
    const response = await genAI.models.generateContent({
      model: 'gemini-2.0-flash',
      contents: prompt,
      config: {
        temperature: 0.3,
        maxOutputTokens: 2048,
      },
    });

    // Extract the text response
    const aiResponse = response.text || '';

    return NextResponse.json({
      response: aiResponse,
      recordsUsed: selectedRecords.length,
    });

  } catch (error) {
    console.error('Gemini API error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to generate response',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
} 