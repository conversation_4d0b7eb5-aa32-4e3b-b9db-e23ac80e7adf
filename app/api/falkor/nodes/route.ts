import { NextRequest, NextResponse } from 'next/server';
import { graphExists } from '@/lib/falkordb';
import { 
  transformJsonToGraph,
  transformHRDataToGraph,
  convertHRDataToLegacyFormat,
  createCategoryNodes,
  createRecordNodes,
  createRelationships,
  upsertCategoryNode,
  upsertRecordNode
} from '@/lib/data-transformer';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      graphName, 
      data, 
      operation = 'create', // 'create' or 'upsert'
      nodeType = 'all' // 'all', 'category', 'record'
    } = body;

    // Validate input
    if (!graphName || typeof graphName !== 'string') {
      return NextResponse.json(
        { error: 'Graph name is required and must be a string' },
        { status: 400 }
      );
    }

    if (!data) {
      return NextResponse.json(
        { error: 'Data is required' },
        { status: 400 }
      );
    }

    // Check if graph exists
    const exists = await graphExists(graphName);
    if (!exists) {
      return NextResponse.json(
        { error: `Graph '${graphName}' does not exist. Create it first.` },
        { status: 404 }
      );
    }

    // Handle bulk data import (full JSON structure)
    if (data.children && typeof data.children === 'object') {
      return await handleBulkImport(graphName, data, operation, nodeType);
    }

    // Handle HR data import (array structure)
    if (operation === 'create_hr' && Array.isArray(data)) {
      return await handleHRDataImport(graphName, data);
    }

    // Handle single node operations
    if (data.nodeType) {
      return await handleSingleNode(graphName, data, operation);
    }

    return NextResponse.json(
      { error: 'Invalid data format. Expected either full JSON structure or single node data.' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error in nodes API:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}

async function handleBulkImport(
  graphName: string, 
  data: any, 
  operation: string,
  nodeType: string
): Promise<NextResponse> {
  try {
    // Transform JSON data to graph structure
    const { categoryNodes, recordNodes, relationships } = transformJsonToGraph(data);
    
    let createdCategories = 0;
    let createdRecords = 0;
    let createdRelationships = 0;

    // Create or upsert category nodes
    if (nodeType === 'all' || nodeType === 'category') {
      if (operation === 'upsert') {
        for (const node of categoryNodes) {
          await upsertCategoryNode(graphName, node);
        }
      } else {
        await createCategoryNodes(graphName, categoryNodes);
      }
      createdCategories = categoryNodes.length;
    }

    // Create or upsert record nodes
    if (nodeType === 'all' || nodeType === 'record') {
      if (operation === 'upsert') {
        for (const node of recordNodes) {
          await upsertRecordNode(graphName, node);
        }
      } else {
        await createRecordNodes(graphName, recordNodes);
      }
      createdRecords = recordNodes.length;
    }

    // Create relationships (only for 'create' operation or when creating all nodes)
    if (operation === 'create' && nodeType === 'all') {
      await createRelationships(graphName, relationships);
      createdRelationships = relationships.length;
    }

    return NextResponse.json({
      success: true,
      message: `Bulk ${operation} operation completed successfully`,
      graphName,
      operation,
      nodeType,
      summary: {
        categoryNodes: createdCategories,
        recordNodes: createdRecords,
        relationships: createdRelationships,
        total: createdCategories + createdRecords
      }
    });

  } catch (error) {
    console.error('Error in bulk import:', error);
    throw error;
  }
}

async function handleHRDataImport(
  graphName: string, 
  hrData: any[]
): Promise<NextResponse> {
  try {
    // Transform HR data to graph structure
    const { categoryNodes, recordNodes, relationships } = transformHRDataToGraph(hrData);
    
    let createdCategories = 0;
    let createdRecords = 0;
    let createdRelationships = 0;

    // Create category nodes
    await createCategoryNodes(graphName, categoryNodes);
    createdCategories = categoryNodes.length;

    // Create record nodes
    await createRecordNodes(graphName, recordNodes);
    createdRecords = recordNodes.length;

    // Create relationships
    await createRelationships(graphName, relationships);
    createdRelationships = relationships.length;

    return NextResponse.json({
      success: true,
      message: `HR data import completed successfully`,
      graphName,
      operation: 'create_hr',
      summary: {
        categoryNodes: createdCategories,
        recordNodes: createdRecords,
        relationships: createdRelationships,
        total: createdCategories + createdRecords
      }
    });

  } catch (error) {
    console.error('Error in HR data import:', error);
    throw error;
  }
}

async function handleSingleNode(
  graphName: string, 
  data: any, 
  operation: string
): Promise<NextResponse> {
  try {
    if (data.nodeType === 'category') {
      if (operation === 'upsert') {
        await upsertCategoryNode(graphName, data);
      } else {
        await createCategoryNodes(graphName, [data]);
      }
      
      return NextResponse.json({
        success: true,
        message: `Category node ${operation}d successfully`,
        graphName,
        operation,
        nodeType: 'category',
        nodeId: data.id,
        nodeName: data.name
      });
    }

    if (data.nodeType === 'record') {
      if (operation === 'upsert') {
        await upsertRecordNode(graphName, data);
      } else {
        await createRecordNodes(graphName, [data]);
      }
      
      return NextResponse.json({
        success: true,
        message: `Record node ${operation}d successfully`,
        graphName,
        operation,
        nodeType: 'record',
        nodeId: data.id,
        nodeTitle: data.title
      });
    }

    return NextResponse.json(
      { error: 'Invalid nodeType. Must be "category" or "record"' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error in single node operation:', error);
    throw error;
  }
}

export async function GET(request: NextRequest) {
  try {
    return NextResponse.json({
      message: 'FalkorDB Nodes API',
      endpoints: {
        'POST /api/falkor/nodes': 'Create or upsert nodes in a graph'
      },
      parameters: {
        graphName: 'Name of the target graph (required)',
        data: 'Node data - either full JSON structure or single node (required)',
        operation: 'Operation type: "create" or "upsert" (default: "create")',
        nodeType: 'Node type filter for bulk operations: "all", "category", or "record" (default: "all")'
      },
      examples: {
        bulkImport: {
          method: 'POST',
          body: {
            graphName: 'knowledge_graph',
            operation: 'create',
            nodeType: 'all',
            data: {
              children: {
                "Category Name": {
                  id: "cat-1",
                  country: "global",
                  summary: "Category summary",
                  refinedSummary: "Refined summary",
                  children: {},
                  records: [
                    {
                      id: "rec-1",
                      fields: {
                        ID: 12345,
                        "HTML URL": "https://example.com",
                        Title: "Example Record",
                        Breadcrumb: "Category > Example",
                        "Is Deepest": "true",
                        Country: "global",
                        "Section ID": 67890,
                        "Cleaned Body": "Record content"
                      }
                    }
                  ]
                }
              }
            }
          }
        },
        singleCategoryNode: {
          method: 'POST',
          body: {
            graphName: 'knowledge_graph',
            operation: 'upsert',
            data: {
              id: 'cat-1',
              name: 'Category Name',
              country: 'global',
              summary: 'Category summary',
              refinedSummary: 'Refined summary',
              nodeType: 'category',
              level: 0,
              path: 'Category Name'
            }
          }
        },
        singleRecordNode: {
          method: 'POST',
          body: {
            graphName: 'knowledge_graph',
            operation: 'upsert',
            data: {
              id: 'rec-1',
              recordId: 12345,
              title: 'Example Record',
              htmlUrl: 'https://example.com',
              breadcrumb: 'Category > Example',
              isDeepest: true,
              country: 'global',
              sectionId: 67890,
              cleanedBody: 'Record content',
              nodeType: 'record'
            }
          }
        }
      }
    });

  } catch (error) {
    console.error('Error in nodes GET API:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}
