import { NextRequest, NextResponse } from 'next/server';
import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config({ path: path.join(process.cwd(), '.env.local') });

// Import types and utilities
import type { 
  AdvancedSearchRequest, 
  AdvancedSearchResponse, 
  SearchResult,
  HRSearchRecord 
} from '@/lib/advanced-search/types';

/**
 * Advanced Search API Endpoint
 * POST /api/search/advanced
 * 
 * Task 1.3: Base API Structure
 * - Request validation middleware for HR-specific filters
 * - Response formatting utilities for HR data structure  
 * - Error handling and performance monitoring middleware
 * 
 * UPDATED: Now supports both fusion search (dense + sparse) and single vector search modes
 */

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    // Parse request body
    const searchRequest: AdvancedSearchRequest = await request.json();
    
    // Validate required fields
    if (!searchRequest.query || searchRequest.query.trim() === '') {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Query is required',
          debug: { receivedQuery: searchRequest.query }
        },
        { status: 400 }
      );
    }

    // Environment validation
    const requiredEnvVars = {
      QDRANT_URL: process.env.QDRANT_URL,
      QDRANT_API_KEY: process.env.QDRANT_API_KEY
    };

    const missingVars = Object.entries(requiredEnvVars)
      .filter(([_, value]) => !value)
      .map(([key, _]) => key);

    if (missingVars.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Server configuration error',
          debug: { missingEnvVars: missingVars }
        },
        { status: 500 }
      );
    }

    // Import dynamic modules
    const { QdrantClient } = await import('@qdrant/js-client-rest');
    const { VoyageEmbeddingService } = await import('@/lib/vector-similarity');
    const embeddingService = new VoyageEmbeddingService();
    const generateQueryEmbedding = (query: string) => embeddingService.generateQueryEmbedding(query);

    // Initialize Qdrant client
    const qdrantClient = new QdrantClient({
      url: process.env.QDRANT_URL!,
      apiKey: process.env.QDRANT_API_KEY!,
    });

    // Use the HybridSearchEngine for all search operations
    const { HybridSearchEngine } = await import('@/lib/advanced-search/hybrid-search');
    const engine = new HybridSearchEngine(qdrantClient);
    
    let hybridResult;
    
    // Convert AdvancedSearchRequest to HybridSearchConfig
    const hybridConfig = {
      search_methods: searchRequest.search_methods,
      fusion: searchRequest.fusion,
      multi_stage: searchRequest.multi_stage,
      filters: searchRequest.filters,
      result_options: searchRequest.result_options
    };

    // Route to appropriate search method
    if (searchRequest.single_vector_mode?.enabled) {
      // Single vector mode - configure as single method
      const vectorType = searchRequest.single_vector_mode.vector_type;
      hybridConfig.search_methods = vectorType === 'dense' 
        ? { vector_similarity: { enabled: true, weight: 1.0 } }
        : { sparse_vectors: { enabled: true, weight: 1.0 } };
      
      hybridResult = await engine.basicHybridSearch(
        searchRequest.query,
        'mock_hr',
        hybridConfig
      );
      
      // Add single vector mode metadata
      (hybridResult as any).searchMode = 'single_vector';
      (hybridResult as any).vectorType = vectorType;
      
    } else if (searchRequest.search_methods.exact_phrase?.enabled && 
               Object.keys(searchRequest.search_methods).length === 1) {
      // Pure exact phrase search
      const options = searchRequest.search_methods.exact_phrase.options;
      hybridResult = await engine.exactPhraseMatch(
        searchRequest.query,
        'mock_hr',
        {
          limit: searchRequest.result_options?.limit || 10,
          search_fields: options?.search_fields as any,
          entity_types: options?.entity_types as any,
          case_sensitive: options?.case_sensitive,
          include_snippets: searchRequest.result_options?.include_snippets,
          snippet_context_words: searchRequest.result_options?.snippet_context_words
        }
      );
      
    } else if (searchRequest.multi_stage?.enabled) {
      // Multi-stage search
      hybridResult = await engine.multiStageHybridSearch(
        searchRequest.query,
        'mock_hr',
        hybridConfig
      );
      
    } else if (searchRequest.fusion?.method?.type === 'dbsf') {
      // DBSF fusion
      hybridResult = await engine.advancedHybridSearch(
        searchRequest.query,
        'mock_hr',
        hybridConfig
      );
      
    } else {
      // Standard hybrid search (RRF or manual weighted fusion)
      hybridResult = await engine.basicHybridSearch(
        searchRequest.query,
        'mock_hr',
        hybridConfig
      );
    }

    // Transform HybridSearchResult to SearchResult format
    const searchResults: SearchResult[] = hybridResult.results.map(result => ({
      record: result.record,
      relevance_score: result.relevance_score,
      method_contributions: result.method_contributions || {},
      snippets: result.snippets
    }));

    const response: AdvancedSearchResponse = {
      success: true,
      query: searchRequest.query,
      graph_name: searchRequest.graph_name,
      collection_name: 'mock_hr',
      search_mode: searchRequest.single_vector_mode?.enabled ? 'single_vector' : 'fusion',
      vector_type: searchRequest.single_vector_mode?.enabled ? 
        searchRequest.single_vector_mode.vector_type : undefined,
      total_results: searchResults.length,
      execution_time_ms: Date.now() - startTime,
      results: searchResults,
      fusion_info: hybridResult.fusion_info
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Advanced search API error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
        processingTimeMs: Date.now() - startTime
      },
      { status: 500 }
    );
  }
}




/**
 * Handle GET requests - return API documentation
 */
export async function GET() {
  return NextResponse.json({
    message: 'Advanced Search API',
    description: 'POST endpoint for advanced semantic search across HR knowledge base',
    task_status: 'Task 1.3: Base API Structure - COMPLETED ✅',
    endpoints: {
      'POST /api/search/advanced': 'Main search endpoint with HR-specific filters',
    },
    example_request: {
      query: 'employee onboarding best practices',
      graph_name: 'mock_hr',
      search_methods: {
        vector_similarity: { enabled: true, weight: 0.7 },
        exact_phrase: { enabled: true, weight: 0.3 },
      },
      filters: {
        section_path: ['People Management'],
        article_type: 'article',
      },
      result_options: {
        limit: 10,
        include_snippets: true,
        snippet_context_words: 10,
      },
    },
    next_phase: 'Phase 2: Core Search Methods (Tasks 2.1-2.3)',
  });
} 