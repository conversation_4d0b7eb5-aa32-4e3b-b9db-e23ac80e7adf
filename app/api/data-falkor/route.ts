import { NextResponse } from 'next/server'
import { retrieveDataFromFalkorDB } from '@/lib/data-transformer'
import { healthCheck } from '@/lib/falkordb'

// Default graph name for the knowledge base
const DEFAULT_GRAPH_NAME = 'mock_hr'

export async function GET(request: Request) {
  try {
    // Parse URL to get parameters
    const url = new URL(request.url)
    const graphName = url.searchParams.get('graph') || DEFAULT_GRAPH_NAME
    
    // Pagination parameters
    const page = parseInt(url.searchParams.get('page') || '0')
    const limit = parseInt(url.searchParams.get('limit') || '1000')
    const offset = page * limit
    
    // Optional health check parameter
    const checkHealth = url.searchParams.get('health') === 'true'
    
    // Validate pagination parameters
    if (page < 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Page parameter must be non-negative'
        },
        { status: 400 }
      )
    }
    
    if (limit <= 0 || limit > 10000) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Limit parameter must be between 1 and 10000'
        },
        { status: 400 }
      )
    }
    
    if (checkHealth) {
      // Perform health check first
      const health = await healthCheck()
      if (health.status !== 'healthy') {
        return NextResponse.json(
          { 
            success: false, 
            error: 'FalkorDB connection unhealthy',
            details: health.message
          },
          { status: 503 }
        )
      }
    }
    
    // Retrieve data from FalkorDB with pagination
    const { nodes, records, pagination } = await retrieveDataFromFalkorDB(graphName, { page, limit, offset })
    
    // Return in the same format as the original API with pagination metadata
    return NextResponse.json({
      success: true,
      data: {
        nodes,
        records
      },
      meta: {
        source: 'falkordb',
        graph: graphName,
        timestamp: new Date().toISOString(),
        pagination: {
          page,
          limit,
          offset,
          has_more: pagination.has_more,
          total_categories: pagination.total_categories,
          total_records: pagination.total_records,
          returned_nodes: nodes.length,
          returned_records: records.length,
        },
        node_count: nodes.length,
        record_count: records.length
      }
    })
    
  } catch (error) {
    console.error('Error in FalkorDB data API:', error)
    
    // Determine error type and status code
    let statusCode = 500
    let errorMessage = 'Failed to load data from FalkorDB'
    
    if (error instanceof Error) {
      if (error.message.includes('Graph') && error.message.includes('does not exist')) {
        statusCode = 404
        errorMessage = 'Knowledge graph not found'
      } else if (error.message.includes('connection')) {
        statusCode = 503
        errorMessage = 'Database connection failed'
      } else {
        errorMessage = error.message
      }
    }
    
    return NextResponse.json(
      { 
        success: false, 
        error: errorMessage,
        details: error instanceof Error ? error.message : 'Unknown error occurred',
        source: 'falkordb'
      },
      { status: statusCode }
    )
  }
}

// Optional POST endpoint for testing or administrative operations
export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { action, graphName = DEFAULT_GRAPH_NAME } = body
    
    if (action === 'health-check') {
      const health = await healthCheck()
      return NextResponse.json({
        success: true,
        health,
        timestamp: new Date().toISOString()
      })
    }
    
    if (action === 'reload-data') {
      // Reload data from the specified graph (without pagination for full reload)
      const { nodes, records } = await retrieveDataFromFalkorDB(graphName)
      
      return NextResponse.json({
        success: true,
        message: 'Data reloaded successfully',
        data: {
          nodes,
          records
        },
        meta: {
          source: 'falkordb',
          graph: graphName,
          timestamp: new Date().toISOString(),
          node_count: nodes.length,
          record_count: records.length
        }
      })
    }
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Invalid action. Supported actions: health-check, reload-data' 
      },
      { status: 400 }
    )
    
  } catch (error) {
    console.error('Error in FalkorDB data API POST:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process request',
        details: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    )
  }
}
