import { NextRequest, NextResponse } from 'next/server';
import { getEvaluationService } from '@/lib/evaluation-utils';
import type { EvaluationRequest } from '@/lib/evaluation-service';

export async function POST(request: NextRequest) {
  try {
    const { query, response, selectedRecords } = await request.json();

    if (!query || !response || !selectedRecords) {
      return NextResponse.json(
        { error: 'Query, response, and selectedRecords are required' },
        { status: 400 }
      );
    }

    // Format the context from selected records (same as Gemini API)
    const context = selectedRecords.map((record: any, index: number) => {
      return `Record ${index + 1}:
Title: ${record.fields.Title || `Record #${record.fields.ID}`}
Content: ${record.fields["Cleaned Body"]}
Source: ${record.fields["HTML URL"]}
Country: ${record.fields.Country}
Section: ${record.fields["Section ID"]}
Breadcrumb: ${record.fields.Breadcrumb}
---`;
    }).join('\n\n');

    console.log('🔍 Starting direct evaluation for response...');
    
    // Run evaluation
    const evaluationService = getEvaluationService();
    const evaluationRequest: EvaluationRequest = {
      question: query,
      answer: response,
      context: context
    };

    const evaluation = await evaluationService.evaluateGroundedness(evaluationRequest);
    
    console.log('✅ Evaluation completed:', {
      overallScore: evaluation.overall_score,
      claimCount: evaluation.claim_analysis.length,
      timestamp: evaluation.evaluation_metadata.timestamp
    });

    return NextResponse.json({
      success: true,
      evaluation,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Direct evaluation failed:', error);
    
    return NextResponse.json(
      { 
        error: 'Evaluation failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
} 