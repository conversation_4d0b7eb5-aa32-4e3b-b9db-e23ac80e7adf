import { NextRequest, NextResponse } from 'next/server';

// In a real implementation, you'd store evaluation results in a database
// For demo purposes, we'll use in-memory storage with TTL
interface CacheEntry {
  evaluation: any;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

const evaluationCache = new Map<string, CacheEntry>();

// Cache cleanup - remove expired entries every 5 minutes
setInterval(() => {
  const now = Date.now();
  let cleaned = 0;
  for (const [key, entry] of evaluationCache.entries()) {
    if (now - entry.timestamp > entry.ttl) {
      evaluationCache.delete(key);
      cleaned++;
    }
  }
  if (cleaned > 0) {
    console.log(`🧹 Cleaned ${cleaned} expired cache entries. Cache size: ${evaluationCache.size}`);
  }
}, 5 * 60 * 1000); // 5 minutes

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const requestId = searchParams.get('id');

    if (!requestId) {
      return NextResponse.json(
        { error: 'Request ID is required' },
        { status: 400 }
      );
    }

    const cacheEntry = evaluationCache.get(requestId);
    const now = Date.now();

    console.log('🔍 Checking evaluation status for:', requestId);
    console.log('📊 Cache contains:', Array.from(evaluationCache.keys()));
    
    // Check if entry exists and hasn't expired
    if (cacheEntry && (now - cacheEntry.timestamp) < cacheEntry.ttl) {
      console.log('📤 Returning completed evaluation with', cacheEntry.evaluation.claim_analysis?.length || 0, 'claims');
      return NextResponse.json({
        status: 'completed',
        evaluation: cacheEntry.evaluation
      });
    } else if (cacheEntry && (now - cacheEntry.timestamp) >= cacheEntry.ttl) {
      // Entry expired
      evaluationCache.delete(requestId);
      console.log('⏰ Cache entry expired for:', requestId);
      return NextResponse.json({
        status: 'expired'
      });
    } else {
      console.log('⏳ Evaluation still processing for:', requestId);
      return NextResponse.json({
        status: 'processing'
      });
    }

  } catch (error) {
    console.error('❌ Evaluation status error:', error);
    return NextResponse.json(
      { error: 'Failed to check evaluation status' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { requestId, evaluation } = body;

    console.log('📥 Received evaluation storage request:', {
      requestId,
      hasEvaluation: !!evaluation,
      evaluationKeys: evaluation ? Object.keys(evaluation) : [],
      claimCount: evaluation?.claim_analysis?.length || 0
    });

    if (!requestId || !evaluation) {
      console.error('❌ Missing required fields:', { requestId: !!requestId, evaluation: !!evaluation });
      return NextResponse.json(
        { error: 'Request ID and evaluation data are required' },
        { status: 400 }
      );
    }

    // Store evaluation result with TTL (24 hours)
    const cacheEntry: CacheEntry = {
      evaluation,
      timestamp: Date.now(),
      ttl: 24 * 60 * 60 * 1000 // 24 hours
    };
    
    evaluationCache.set(requestId, cacheEntry);
    console.log('✅ Successfully stored evaluation for:', requestId);
    console.log('📊 Cache now contains:', evaluationCache.size, 'evaluations');

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('❌ Store evaluation error:', error);
    return NextResponse.json(
      { error: 'Failed to store evaluation' },
      { status: 500 }
    );
  }
}