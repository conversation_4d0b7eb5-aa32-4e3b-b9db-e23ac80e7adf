"use client"

import { useMemo, useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, Check, FileText, BarChart3, CheckCircle, AlertTriangle, XCircle, Quote, X, ExternalLink, ArrowLeft } from "lucide-react"
import { cn } from "@/lib/utils"
import type { Record } from "@/types"

interface Snippet {
  start_words: string
  end_words: string
}

interface ClaimAnalysis {
  claim: string
  supporting_context_snippets: Snippet[]
  reasoning: string
  groundedness_score: number
}

interface EvalData {
  claim_analysis: ClaimAnalysis[]
}

interface LlmResponse {
  response: string
  evals: EvalData
}

type ModalView = 'evaluation' | 'record'

interface ResponseModalProps {
  response: LlmResponse | null
  selectedRecords?: Record[]
  isOpen: boolean
  onClose: () => void
  isEvaluationLoading?: boolean
}

export default function ResponseModal({ 
  response, 
  selectedRecords = [], 
  isOpen, 
  onClose, 
  isEvaluationLoading = false
}: ResponseModalProps) {
  const [copied, setCopied] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)
  const [currentView, setCurrentView] = useState<ModalView>('evaluation')
  const [selectedRecord, setSelectedRecord] = useState<Record | null>(null)
  const [highlightText, setHighlightText] = useState<{ start: string, end: string } | undefined>()

  // Create highlighted content when highlightText is provided
  const highlightedContent = useMemo(() => {
    if (!selectedRecord?.fields["Cleaned Body"] || !highlightText) {
      return selectedRecord?.fields["Cleaned Body"] || ""
    }

    const content = selectedRecord.fields["Cleaned Body"]
    const { start, end } = highlightText
    
    // Find the start and end positions in the content
    const startIndex = content.toLowerCase().indexOf(start.toLowerCase())
    const endIndex = content.toLowerCase().indexOf(end.toLowerCase())
    
    if (startIndex === -1 || endIndex === -1) {
      return content // Return original if not found
    }
    
    // Determine the range to highlight
    const highlightStart = startIndex
    const highlightEnd = endIndex + end.length
    
    // Split content into before, highlighted, and after sections
    const before = content.substring(0, highlightStart)
    const highlighted = content.substring(highlightStart, highlightEnd)
    const after = content.substring(highlightEnd)
    
    return { before, highlighted, after }
  }, [selectedRecord?.fields["Cleaned Body"], highlightText])

  const stats = useMemo(() => {
    if (!response || !response.evals.claim_analysis.length) return null
    const claims = response.evals.claim_analysis
    const total = claims.length
    const avg = claims.reduce((sum, c) => sum + c.groundedness_score, 0) / total
    const high = claims.filter((c) => c.groundedness_score >= 90).length
    const medium = claims.filter((c) => c.groundedness_score >= 70 && c.groundedness_score < 90).length
    const low = claims.filter((c) => c.groundedness_score < 70).length
    return { total, avg: Math.round(avg), high, medium, low }
  }, [response])

  useEffect(() => {
    if (isOpen) {
      setIsAnimating(true)
      // Prevent body scroll when modal is open
      document.body.style.overflow = "hidden"
    } else {
      // Re-enable body scroll when modal closes
      document.body.style.overflow = "unset"
      // Reset view when modal closes
      setCurrentView('evaluation')
      setSelectedRecord(null)
      setHighlightText(undefined)
    }

    return () => {
      document.body.style.overflow = "unset"
    }
  }, [isOpen])

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isOpen) {
        if (currentView === 'record') {
          // If viewing record, go back to evaluation view
          setCurrentView('evaluation')
          setSelectedRecord(null)
          setHighlightText(undefined)
        } else {
          // If in evaluation view, close modal
          handleClose()
        }
      }
    }

    if (isOpen) {
      document.addEventListener("keydown", handleEscape)
    }

    return () => {
      document.removeEventListener("keydown", handleEscape)
    }
  }, [isOpen, currentView])

  const handleClose = () => {
    setIsAnimating(false)
    setTimeout(() => {
      onClose()
    }, 300) // Match animation duration
  }

  const handleCopy = async () => {
    if (!response) return
    try {
      await navigator.clipboard.writeText(response.response)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error("Failed to copy:", error)
    }
  }

  const findRecordForSnippet = (snippet: Snippet): Record | null => {
    if (!selectedRecords || selectedRecords.length === 0) return null
    
    const normalizeText = (text: string): string => {
      return text.toLowerCase().replace(/\s+/g, ' ').trim()
    }
    
    const searchTexts = [
      snippet.start_words,
      snippet.end_words,
      `${snippet.start_words} ${snippet.end_words}`
    ].map(normalizeText)
    
    for (const record of selectedRecords) {
      const cleanedBody = record.fields["Cleaned Body"] || ""
      const title = record.fields.Title || ""
      const searchContent = normalizeText(`${title} ${cleanedBody}`)
      
      // Check if any of the search texts exist in this record
      const hasMatch = searchTexts.some(searchText => 
        searchText.length > 0 && searchContent.includes(searchText)
      )
      
      if (hasMatch) {
        return record
      }
    }
    
    return null
  }

  // New function to extract context with highlighting
  const extractContextWithHighlighting = (snippet: Snippet, sourceRecord: Record | null): {
    contextText: string;
    highlightedHtml: string;
  } | null => {
    if (!sourceRecord) return null;
    
    const cleanedBody = sourceRecord.fields["Cleaned Body"] || "";
    const title = sourceRecord.fields.Title || "";
    const fullText = `${title} ${cleanedBody}`;
    
    // Normalize search terms
    const startWords = snippet.start_words.trim();
    const endWords = snippet.end_words.trim();
    
    if (!startWords || !endWords) return null;
    
    // Create regex patterns for case-insensitive search
    const escapeRegex = (text: string) => text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const startPattern = new RegExp(escapeRegex(startWords), 'gi');
    const endPattern = new RegExp(escapeRegex(endWords), 'gi');
    
    // Find start position
    const startMatch = startPattern.exec(fullText);
    if (!startMatch) return null;
    
    const startIndex = startMatch.index;
    
    // Find end position (search from start position)
    endPattern.lastIndex = startIndex;
    const endMatch = endPattern.exec(fullText);
    if (!endMatch) return null;
    
    const endIndex = endMatch.index + endMatch[0].length;
    
    // Extract words before start (up to 10 words)
    const beforeText = fullText.substring(0, startIndex);
    const beforeWords = beforeText.split(/\s+/).filter(word => word.length > 0);
    const contextBefore = beforeWords.slice(-10).join(' ');
    
    // Extract words after end (up to 10 words)
    const afterText = fullText.substring(endIndex);
    const afterWords = afterText.split(/\s+/).filter(word => word.length > 0);
    const contextAfter = afterWords.slice(0, 10).join(' ');
    
    // Extract the highlighted content (from start to end)
    const highlightedContent = fullText.substring(startIndex, endIndex);
    
    // Build the full context text
    const contextText = [
      contextBefore && '...',
      contextBefore,
      highlightedContent,
      contextAfter,
      contextAfter && '...'
    ].filter(Boolean).join(' ');
    
    // Build HTML with highlighting
    const highlightedHtml = [
      contextBefore && `<span class="text-gray-600">...${contextBefore}</span>`,
      `<span class="bg-yellow-200 px-1 rounded font-medium">${highlightedContent}</span>`,
      contextAfter && `<span class="text-gray-600">${contextAfter}...</span>`
    ].filter(Boolean).join(' ');
    
    return {
      contextText,
      highlightedHtml
    };
  };

  // Handle clicking on a supporting evidence snippet
  const handleSnippetClick = (snippet: Snippet) => {
    const sourceRecord = findRecordForSnippet(snippet)
    if (sourceRecord) {
      setSelectedRecord(sourceRecord)
      setHighlightText({
        start: snippet.start_words,
        end: snippet.end_words
      })
      setCurrentView('record')
    }
  }

  // Back to evaluation view
  const handleBackToEvaluation = () => {
    setCurrentView('evaluation')
    setSelectedRecord(null)
    setHighlightText(undefined)
  }

  // Record modal utility functions
  const getBM42ScoreColor = (score: number) => {
    if (score >= 8) return "bg-green-500"
    if (score >= 6) return "bg-yellow-500"
    return "bg-red-500"
  }

  const getMethodBadgeVariant = (method: string) => {
    switch (method) {
      case "bm42":
        return "default"
      case "text-search":
        return "secondary"
      case "urls":
        return "outline"
      default:
        return "secondary"
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-emerald-700 bg-emerald-50 border-emerald-200"
    if (score >= 70) return "text-amber-700 bg-amber-50 border-amber-200"
    return "text-red-700 bg-red-50 border-red-200"
  }

  const getScoreIcon = (score: number) => {
    if (score >= 90) return <CheckCircle className="h-4 w-4 text-emerald-600" />
    if (score >= 70) return <AlertTriangle className="h-4 w-4 text-amber-600" />
    return <XCircle className="h-4 w-4 text-red-600" />
  }

  const getScoreBorderColor = (score: number) => {
    if (score >= 90) return "border-l-emerald-500"
    if (score >= 70) return "border-l-amber-500"
    return "border-l-red-500"
  }

  const getProgressBarColor = (score: number) => {
    if (score >= 90) return "bg-green-300"
    if (score >= 70) return "bg-orange-300"
    return "bg-red-300"
  }

  if (!isOpen) return null

  // Record Detail View
  if (currentView === 'record' && selectedRecord) {
    return (
      <>
        {/* Backdrop */}
        <div
          className={cn(
            "fixed inset-0 bg-black/20 backdrop-blur-sm z-40 transition-opacity duration-300",
            isAnimating ? "opacity-100" : "opacity-0",
          )}
          onClick={handleClose}
        />

        {/* Modal */}
        <div
          className={cn(
            "fixed inset-x-0 bottom-0 z-50 bg-white rounded-t-2xl shadow-2xl transition-transform duration-300 ease-out",
            "h-[90vh] flex flex-col",
            isAnimating ? "translate-y-0" : "translate-y-full",
          )}
        >
          {/* Handle bar */}
          <div className="flex justify-center pt-3 pb-2">
            <div className="w-12 h-1 bg-muted-foreground/30 rounded-full" />
          </div>

          {/* Header */}
          <div className="flex items-center justify-between px-4 pb-4 border-b border-border">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBackToEvaluation}
                className="h-8 px-2"
              >
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Results
              </Button>
              <h2 className="text-lg font-semibold">Record #{selectedRecord.fields.ID}</h2>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={() => window.open(selectedRecord.fields["HTML URL"], "_blank")}
              >
                <ExternalLink className="h-4 w-4" />
              </Button>
            </div>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={handleClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto overscroll-contain p-4">
            <div className="max-w-full mx-auto space-y-6 pb-6">
              {/* Metadata Section */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base">Metadata</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Title */}
                  {selectedRecord.fields.Title && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Title</label>
                      <p className="text-sm mt-1">{selectedRecord.fields.Title}</p>
                    </div>
                  )}

                  {/* Breadcrumb */}
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Location</label>
                    <p className="text-sm mt-1 text-muted-foreground">{selectedRecord.fields.Breadcrumb}</p>
                  </div>

                  {/* URL */}
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">URL</label>
                    <a
                      href={selectedRecord.fields["HTML URL"]}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm mt-1 text-primary hover:underline block break-all"
                    >
                      {selectedRecord.fields["HTML URL"]}
                    </a>
                  </div>

                  {/* Badges */}
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="secondary">{selectedRecord.fields.Country}</Badge>
                    <Badge variant="outline">Section {selectedRecord.fields["Section ID"]}</Badge>
                    {selectedRecord.fields["Is Deepest"] === "Yes" && <Badge variant="default">Deepest</Badge>}
                    <Badge variant={getMethodBadgeVariant(selectedRecord._search_method)}>{selectedRecord._search_method}</Badge>
                    <div
                      className={`px-2 py-1 rounded text-white text-xs font-medium ${getBM42ScoreColor(selectedRecord._bm42_score)}`}
                    >
                      BM42: {selectedRecord._bm42_score.toFixed(1)}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Content Section */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base">Content</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="prose prose-sm max-w-none">
                    <div className="whitespace-pre-wrap text-sm leading-relaxed">
                      {typeof highlightedContent === 'string' ? (
                        highlightedContent
                      ) : (
                        <>
                          {highlightedContent.before}
                          <mark className="bg-yellow-200 px-1 py-0.5 rounded">
                            {highlightedContent.highlighted}
                          </mark>
                          {highlightedContent.after}
                        </>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Actions */}
              <div className="flex gap-3 pt-4">
                <Button onClick={() => window.open(selectedRecord.fields["HTML URL"], "_blank")} className="flex-1">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Open Original
                </Button>
                <Button variant="outline" onClick={handleBackToEvaluation} className="flex-1">
                  Back to Results
                </Button>
              </div>
            </div>
          </div>
        </div>
      </>
    )
  }

  // Default to null if no response
  if (!response) return null

  // Evaluation View (default)
  return (
    <>
      {/* Backdrop */}
      <div
        className={cn(
          "fixed inset-0 bg-black/20 backdrop-blur-sm z-40 transition-opacity duration-300",
          isAnimating ? "opacity-100" : "opacity-0",
        )}
        onClick={handleClose}
      />

      {/* Slide-up Modal */}
      <div
        className={cn(
          "fixed inset-x-0 bottom-0 z-50 bg-white rounded-t-2xl shadow-2xl transition-transform duration-300 ease-out",
          "h-[90vh] flex flex-col",
          isAnimating ? "translate-y-0" : "translate-y-full",
        )}
      >
        {/* Simplified Header */}
        <div className="px-6 py-4 border-b flex items-center justify-between flex-shrink-0">
          {stats && (
            <Badge variant="outline" className={cn("px-3 py-1.5 font-medium", getScoreColor(stats.avg))}>
              <BarChart3 className="h-4 w-4 mr-1.5" />
              Overall Score: {stats.avg}%
            </Badge>
          )}
          <Button variant="ghost" size="sm" onClick={handleClose} className="h-8 w-8 p-0">
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto px-6 py-6">
          <div className="space-y-8 max-w-4xl mx-auto">
            {/* Response Section - Light Purple Theme */}
            <div className="bg-white border-2 border-gray-200 rounded-xl shadow-sm">
              <div className="bg-gray-50 px-6 py-4 border-b border-gray-200 rounded-t-xl">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2">
                    <FileText className="h-5 w-5 text-purple-600" />
                    Generated Response
                  </h3>
                  <Button
                    onClick={handleCopy}
                    variant="outline"
                    size="sm"
                    className={cn(
                      "transition-all duration-200",
                      copied
                        ? "bg-emerald-50 border-emerald-200 text-emerald-700 hover:bg-emerald-100"
                        : "bg-white hover:bg-gray-50",
                    )}
                  >
                    {copied ? (
                      <>
                        <Check className="h-4 w-4 mr-2" />
                        Copied!
                      </>
                    ) : (
                      <>
                        <Copy className="h-4 w-4 mr-2" />
                        Copy Response
                      </>
                    )}
                  </Button>
                </div>
              </div>
              <div className="p-6 bg-white">
                <div className="bg-purple-50 text-purple-900 rounded-lg p-6 font-mono text-sm leading-relaxed whitespace-pre-wrap border border-purple-100">
                  {response.response}
                </div>
              </div>
            </div>

            {/* Evaluation Overview - Pastel Colors */}
            {stats ? (
              <Card>
                <CardHeader className="pb-4">
                  <CardTitle className="text-lg font-medium text-gray-900 flex items-center gap-2">
                    <BarChart3 className="h-5 w-5 text-purple-600" />
                    Evaluation Overview
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-4 gap-6 mb-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-slate-600 mb-1">{stats.total}</div>
                      <div className="text-sm text-gray-600">Total Claims</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-400 mb-1">{stats.high}</div>
                      <div className="text-sm text-gray-600">High Confidence</div>
                      <div className="text-xs text-gray-500">≥90%</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-orange-300 mb-1">{stats.medium}</div>
                      <div className="text-sm text-gray-600">Medium Confidence</div>
                      <div className="text-xs text-gray-500">70-89%</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-red-300 mb-1">{stats.low}</div>
                      <div className="text-sm text-gray-600">Low Confidence</div>
                      <div className="text-xs text-gray-500">&lt;70%</div>
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-700">Overall Groundedness</span>
                      <span className="text-sm font-bold text-gray-900">{stats.avg}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={cn("h-2 rounded-full transition-all duration-500", getProgressBarColor(stats.avg))}
                        style={{ width: `${stats.avg}%` }}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardHeader className="pb-4">
                  <CardTitle className="text-lg font-medium text-gray-900 flex items-center gap-2">
                    <BarChart3 className="h-5 w-5 text-purple-600" />
                    Evaluation Overview
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    {isEvaluationLoading ? (
                      <>
                        <div className="text-purple-400 mb-2">
                          <div className="w-8 h-8 border-2 border-purple-400 border-t-transparent rounded-full animate-spin mx-auto" />
                        </div>
                        <p className="text-sm text-gray-600">
                          Evaluating response claims...
                        </p>
                      </>
                    ) : (
                      <>
                        <div className="text-gray-400 mb-2">
                          <AlertTriangle className="h-8 w-8 mx-auto" />
                        </div>
                        <p className="text-sm text-gray-600">
                          Evaluation data not available for this response.
                        </p>
                      </>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Claims Analysis */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-6 flex items-center gap-2">
                <Quote className="h-5 w-5 text-purple-600" />
                Detailed Claim Analysis ({response.evals.claim_analysis.length})
              </h3>
              <div className="space-y-6">
                {response.evals.claim_analysis.length > 0 ? response.evals.claim_analysis.map((claim, idx) => (
                  <Card
                    key={idx}
                    className={cn(
                      "border-l-4 shadow-sm hover:shadow-md transition-shadow",
                      getScoreBorderColor(claim.groundedness_score),
                    )}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between gap-4">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-3">
                            <Badge variant="outline" className="text-xs font-medium">
                              Claim {idx + 1}
                            </Badge>
                            <Badge className={cn("text-xs font-medium", getScoreColor(claim.groundedness_score))}>
                              {getScoreIcon(claim.groundedness_score)}
                              <span className="ml-1">{claim.groundedness_score}%</span>
                            </Badge>
                          </div>
                          <p className="text-sm font-medium text-gray-900 leading-relaxed">{claim.claim}</p>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0 space-y-4">
                      {/* Supporting Context */}
                      {claim.supporting_context_snippets.length > 0 && (
                        <div>
                          <h4 className="text-xs font-semibold text-gray-700 mb-2 flex items-center gap-1">
                            <Quote className="h-3 w-3" />
                            Supporting Evidence ({claim.supporting_context_snippets.length})
                          </h4>
                          <div className="space-y-2">
                            {claim.supporting_context_snippets.map((snippet, snippetIndex) => {
                              const sourceRecord = findRecordForSnippet(snippet)
                              const contextData = extractContextWithHighlighting(snippet, sourceRecord)
                              
                              return (
                                <div
                                  key={snippetIndex}
                                  className={cn(
                                    "bg-blue-50 border border-blue-200 rounded-md p-3 transition-all duration-200",
                                    sourceRecord 
                                      ? "cursor-pointer hover:bg-blue-100 hover:border-blue-300 hover:shadow-sm" 
                                      : "cursor-not-allowed opacity-75"
                                  )}
                                  onClick={() => handleSnippetClick(snippet)}
                                  title={sourceRecord 
                                    ? `Click to view in: ${sourceRecord.fields.Title || 'Untitled Record'}` 
                                    : "Source record not found"
                                  }
                                >
                                  <div className="flex items-start justify-between">
                                    <div className="text-xs text-blue-800 font-medium flex-1">
                                      {contextData ? (
                                        <div 
                                          className="leading-relaxed"
                                          dangerouslySetInnerHTML={{ __html: contextData.highlightedHtml }}
                                        />
                                      ) : (
                                        <>
                                          &quot;{snippet.start_words}&quot;
                                          {snippet.start_words !== snippet.end_words && (
                                            <>
                                              <span className="mx-2 text-blue-500 font-normal">...</span>&quot;{snippet.end_words}&quot;
                                            </>
                                          )}
                                        </>
                                      )}
                                    </div>
                                    {sourceRecord && (
                                      <ExternalLink className="h-3 w-3 text-blue-600 ml-2 flex-shrink-0" />
                                    )}
                                  </div>
                                  {sourceRecord && (
                                    <div className="text-xs text-blue-600 mt-1 font-medium">
                                      {sourceRecord.fields.Title || `Record #${sourceRecord.fields.ID}`}
                                    </div>
                                  )}
                                </div>
                              )
                            })}
                          </div>
                        </div>
                      )}

                      {/* Reasoning */}
                      <div>
                        <h4 className="text-xs font-semibold text-gray-700 mb-2">Analysis Reasoning</h4>
                        <div className="bg-gray-50 border border-gray-200 rounded-md p-3">
                          <p className="text-xs text-gray-700 leading-relaxed">{claim.reasoning}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )) : (
                  <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200">
                    <div className="text-gray-400 mb-2">
                      <Quote className="h-8 w-8 mx-auto" />
                    </div>
                    <p className="text-sm text-gray-600">
                      No claims to analyze in this response.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
