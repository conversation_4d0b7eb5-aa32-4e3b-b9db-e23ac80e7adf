/**
 * SPLADE Service - RunPod Integration
 * 
 * Implements sparse vector generation using SPLADE v3 model via RunPod API
 * for advanced search capabilities with named vector support.
 */

export interface SpladeVector {
  indices: number[];
  values: number[];
}

export interface RunPodSpladeResponse {
  delayTime: number;
  executionTime: number;
  id: string;
  output: {
    data: Array<{
      embedding: number[];
      index: number;
      object: string;
    }>;
    model: string;
    object: string;
    usage: {
      prompt_tokens: number;
      total_tokens: number;
    };
  };
  status: string;
  workerId?: string;
}

export class SpladeService {
  private runpodUrl: string;
  private apiKey: string;
  private maxRetries = 3;
  private retryDelay = 1000; // 1 second

  constructor() {
    if (!process.env.RUNPOD_API_KEY) {
      throw new Error('RUNPOD_API_KEY environment variable is required');
    }
    if (!process.env.RUNPOD_ENDPOINT_ID) {
      throw new Error('RUNPOD_ENDPOINT_ID environment variable is required');
    }

    this.apiKey = process.env.RUNPOD_API_KEY;
    this.runpodUrl = `https://api.runpod.ai/v2/${process.env.RUNPOD_ENDPOINT_ID}/runsync`;
  }

  /**
   * Generate sparse vector for a single text using RunPod SPLADE API
   */
  async generateSparseVector(text: string): Promise<SpladeVector> {
    if (!text?.trim()) {
      return { indices: [], values: [] };
    }

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        const response = await fetch(this.runpodUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`
          },
          body: JSON.stringify({
            input: {
              model: "naver/splade-v3",
              input: text.trim()
            }
          })
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`RunPod API error (${response.status}): ${errorText}`);
        }

        const data: RunPodSpladeResponse = await response.json();

        if (data.status !== 'COMPLETED') {
          throw new Error(`RunPod execution failed with status: ${data.status}`);
        }

        // Validate the response format - RunPod returns dense embeddings, need to convert to sparse
        if (!data.output || !data.output.data || !Array.isArray(data.output.data) || data.output.data.length === 0) {
          throw new Error('Invalid RunPod response format');
        }

        const embeddingData = data.output.data[0];
        if (!embeddingData.embedding || !Array.isArray(embeddingData.embedding)) {
          throw new Error('No embedding data in RunPod response');
        }

        // Convert dense embedding to sparse format (indices and values)
        const embedding = embeddingData.embedding;
        const indices: number[] = [];
        const values: number[] = [];
        
        // Extract non-zero values and their indices
        for (let i = 0; i < embedding.length; i++) {
          if (Math.abs(embedding[i]) > 1e-8) { // Threshold for considering a value as non-zero
            indices.push(i);
            values.push(embedding[i]);
          }
        }

        return {
          indices,
          values
        };

      } catch (error) {
        console.error(`SPLADE generation attempt ${attempt}/${this.maxRetries} failed:`, error);
        
        if (attempt === this.maxRetries) {
          console.error(`Failed to generate SPLADE vector after ${this.maxRetries} attempts for text: ${text.slice(0, 50)}...`);
          return { indices: [], values: [] };
        }

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt));
      }
    }

    return { indices: [], values: [] };
  }

  /**
   * Generate sparse vectors for multiple texts with batch processing
   */
  async generateSparseVectors(texts: string[], batchSize: number = 5): Promise<SpladeVector[]> {
    if (!texts || texts.length === 0) {
      return [];
    }

    const results: SpladeVector[] = [];
    
    for (let i = 0; i < texts.length; i += batchSize) {
      const batch = texts.slice(i, i + batchSize);
      const batchNumber = Math.floor(i / batchSize) + 1;
      const totalBatches = Math.ceil(texts.length / batchSize);
      
      console.log(`Processing SPLADE batch ${batchNumber}/${totalBatches} (${batch.length} texts)`);
      
      // Process batch in parallel
      const batchPromises = batch.map(text => this.generateSparseVector(text));
      const batchResults = await Promise.all(batchPromises);
      
      results.push(...batchResults);
      
      // Add delay between batches to avoid rate limiting
      if (i + batchSize < texts.length) {
        console.log('Waiting 2 seconds before next batch to avoid rate limits...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    const successCount = results.filter(r => r.indices.length > 0).length;
    console.log(`SPLADE generation completed: ${successCount}/${texts.length} successful`);

    return results;
  }

  /**
   * Convert sparse vector to Qdrant sparse vector format
   */
  formatForQdrant(sparseVector: SpladeVector) {
    return {
      indices: sparseVector.indices,
      values: sparseVector.values
    };
  }

  /**
   * Test SPLADE service connectivity
   */
  async testConnection(): Promise<boolean> {
    try {
      const testResult = await this.generateSparseVector("test connection");
      return testResult.indices.length >= 0; // Even empty result indicates successful connection
    } catch (error) {
      console.error('SPLADE service connection test failed:', error);
      return false;
    }
  }

  /**
   * Get service status information
   */
  getStatus() {
    return {
      service: 'SPLADE via RunPod',
      model: 'naver/splade-v3',
      endpoint: this.runpodUrl,
      configured: !!(this.apiKey && process.env.RUNPOD_ENDPOINT_ID)
    };
  }
}

// Export singleton instance
export const spladeService = new SpladeService();

// Export utility functions
export async function generateSingleSpladeVector(text: string): Promise<SpladeVector> {
  return spladeService.generateSparseVector(text);
}

export async function generateBatchSpladeVectors(texts: string[], batchSize?: number): Promise<SpladeVector[]> {
  return spladeService.generateSparseVectors(texts, batchSize);
}

export async function testSpladeService(): Promise<boolean> {
  return spladeService.testConnection();
} 