/**
 * Advanced Search API - Task 2 Test Suite
 * 
 * Task 2.1: Exact Matching Implementation Tests
 * Task 2.2: Vector Similarity Setup Tests
 * 
 * Test both exact matching algorithms and vector similarity search with HR content
 */

import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config({ path: path.join(process.cwd(), '.env.local') });

// Import test modules
import {
  exactWordMatch,
  exactPhraseMatch,
  ExactMatchingUtils,
  DEFAULT_EXACT_MATCHING_CONFIG,
  type ExactMatchingConfig
} from '@/lib/exact-matching';

import {
  VoyageEmbeddingService,
  VectorSimilaritySearch,
  createVectorSimilarityService,
  VectorUtils,
  DEFAULT_VECTOR_CONFIG,
  type VectorSimilarityConfig
} from '@/lib/vector-similarity';

import type { HRSearchRecord, SearchResult } from './types';

// Default HR collection for testing
const DEFAULT_HR_COLLECTION = 'mock_hr';

/**
 * Sample HR test data for comprehensive testing
 */
const HR_TEST_RECORDS: HRSearchRecord[] = [
  {
    id: 'hr-test-1',
    title: 'Employee Onboarding Best Practices',
    content: 'A comprehensive guide to onboarding new employees effectively. This includes setting up workspace, introduction to team members, training schedule, and performance expectations.',
    summary: 'Best practices for employee onboarding process',
    fullContent: 'A comprehensive guide to onboarding new employees effectively. This includes setting up workspace, introduction to team members, training schedule, and performance expectations. The onboarding process should begin before the employee\'s first day and continue for several weeks.',
    breadcrumb: 'Employers and Admins > People Management > Hiring and Onboarding',
    section_path: ['Employers and Admins', 'People Management', 'Hiring and Onboarding'],
    article_type: 'article',
    level: 3,
    nodeType: 'record',
    country: 'hr',
    htmlUrl: 'https://help.example.com/hr/onboarding-best-practices',
  },
  {
    id: 'hr-test-2',
    title: 'Form I-9 Compliance Guidelines',
    content: 'Understanding I-9 requirements for employment verification. Employers must verify employee identity and work authorization within three business days of hire.',
    summary: 'I-9 compliance for employment verification',
    fullContent: 'Understanding I-9 requirements for employment verification. Employers must verify employee identity and work authorization within three business days of hire. This process is critical for legal compliance.',
    breadcrumb: 'Employers and Admins > People Management > Hiring and Onboarding > US Employees',
    section_path: ['Employers and Admins', 'People Management', 'Hiring and Onboarding', 'US Employees'],
    article_type: 'article',
    level: 4,
    nodeType: 'record',
    country: 'hr',
    htmlUrl: 'https://help.example.com/hr/form-i9-compliance',
  },
  {
    id: 'hr-test-3',
    title: 'Payroll Tax Requirements',
    content: 'Overview of payroll tax obligations including FUTA, SUI, and withholding requirements for different employee types.',
    summary: 'Payroll tax compliance overview',
    fullContent: 'Overview of payroll tax obligations including FUTA, SUI, and withholding requirements for different employee types. Understanding these requirements is essential for HR compliance.',
    breadcrumb: 'Employers and Admins > Payroll > Tax Information',
    section_path: ['Employers and Admins', 'Payroll', 'Tax Information'],
    article_type: 'article',
    level: 3,
    nodeType: 'record',
    country: 'hr',
    htmlUrl: 'https://help.example.com/hr/payroll-tax-requirements',
  },
  {
    id: 'hr-test-4',
    title: 'International Contractor Payments',
    content: 'Key considerations for paying international contractors including tax implications, payment methods, and compliance requirements.',
    summary: 'International contractor payment guide',
    fullContent: 'Key considerations for paying international contractors including tax implications, payment methods, and compliance requirements. This covers both legal and practical aspects of international payments.',
    breadcrumb: 'Employers and Admins > Hiring and Onboarding > International Contractors',
    section_path: ['Employers and Admins', 'Hiring and Onboarding', 'International Contractors'],
    article_type: 'article',
    level: 3,
    nodeType: 'record',
    country: 'hr',
    htmlUrl: 'https://help.example.com/hr/international-contractor-payments',
  }
];

/**
 * Test queries for HR domain validation
 */
const HR_TEST_QUERIES = [
  {
    query: 'employee onboarding',
    expectedRecords: ['hr-test-1'],
    searchType: 'exact_words'
  },
  {
    query: 'best practices for onboarding new employees',
    expectedRecords: ['hr-test-1'],
    searchType: 'exact_phrase'
  },
  {
    query: 'I-9 compliance',
    expectedRecords: ['hr-test-2'],
    searchType: 'exact_words'
  },
  {
    query: 'payroll tax FUTA',
    expectedRecords: ['hr-test-3'],
    searchType: 'exact_words'
  },
  {
    query: 'international contractor payments',
    expectedRecords: ['hr-test-4'],
    searchType: 'vector_similarity'
  }
];

/**
 * Task 2.1 Tests: Exact Matching Implementation
 */
export async function testTask21ExactMatching(): Promise<{ success: boolean; results: any[] }> {
  console.log('\n🧪 Task 2.1: Testing Exact Matching Implementation');
  console.log('==================================================');

  const results: any[] = [];

  try {
    // Test 1: Exact Word Matching
    console.log('\n📝 Test 1: Exact Word Matching');
    const wordQuery = 'employee onboarding';
    const wordResults = exactWordMatch(wordQuery, HR_TEST_RECORDS);
    
    console.log(`   Query: "${wordQuery}"`);
    console.log(`   Results: ${wordResults.length} matches`);
    
    if (wordResults.length > 0) {
      console.log(`   Top result: "${wordResults[0].record.title}" (score: ${wordResults[0].relevance_score.toFixed(3)})`);
      console.log(`   Method contributions:`, wordResults[0].method_contributions);
    }
    
    results.push({
      test: 'exact_word_matching',
      query: wordQuery,
      results_count: wordResults.length,
      success: wordResults.length > 0,
      top_result: wordResults[0]?.record.title || null
    });

    // Test 2: Exact Phrase Matching
    console.log('\n📝 Test 2: Exact Phrase Matching');
    const phraseQuery = 'employment verification';
    const phraseResults = exactPhraseMatch(phraseQuery, HR_TEST_RECORDS);
    
    console.log(`   Query: "${phraseQuery}"`);
    console.log(`   Results: ${phraseResults.length} matches`);
    
    if (phraseResults.length > 0) {
      console.log(`   Top result: "${phraseResults[0].record.title}" (score: ${phraseResults[0].relevance_score.toFixed(3)})`);
      console.log(`   Snippets: ${phraseResults[0].snippets?.length || 0}`);
      
      if (phraseResults[0].snippets && phraseResults[0].snippets.length > 0) {
        console.log(`   Sample snippet: "${phraseResults[0].snippets[0].text}"`);
      }
    }
    
    results.push({
      test: 'exact_phrase_matching',
      query: phraseQuery,
      results_count: phraseResults.length,
      success: phraseResults.length > 0,
      snippets_count: phraseResults[0]?.snippets?.length || 0
    });

    // Test 3: Custom Configuration
    console.log('\n📝 Test 3: Custom Configuration');
    const customConfig: ExactMatchingConfig = {
      ...DEFAULT_EXACT_MATCHING_CONFIG,
      scoreBoosts: {
        titleMatch: 5.0,
        contentMatch: 1.0,
        breadcrumbMatch: 1.5
      }
    };
    
    const customResults = exactWordMatch('hiring', HR_TEST_RECORDS, customConfig);
    console.log(`   Custom config results: ${customResults.length} matches`);
    
    results.push({
      test: 'custom_configuration',
      query: 'hiring',
      results_count: customResults.length,
      success: customResults.length > 0,
      config_applied: true
    });

    // Test 4: Utility Functions
    console.log('\n📝 Test 4: Utility Functions');
    const tokens = ExactMatchingUtils.tokenizeText('Employee Onboarding Best Practices', DEFAULT_EXACT_MATCHING_CONFIG);
    const phraseMatches = ExactMatchingUtils.findPhraseMatches('best practices', 'Employee Onboarding Best Practices for HR');
    
    console.log(`   Tokenization: "${tokens.join(', ')}"`);
    console.log(`   Phrase matches: ${phraseMatches.length}`);
    
    results.push({
      test: 'utility_functions',
      tokenization_success: tokens.length > 0,
      phrase_matching_success: phraseMatches.length > 0,
      success: tokens.length > 0 && phraseMatches.length > 0
    });

    console.log('\n✅ Task 2.1 Exact Matching Tests Completed');
    return { success: true, results };

  } catch (error) {
    console.error('\n❌ Task 2.1 Tests Failed:', error);
    return { success: false, results };
  }
}

/**
 * Task 2.2 Tests: Vector Similarity Setup
 */
export async function testTask22VectorSimilarity(): Promise<{ success: boolean; results: any[] }> {
  console.log('\n🧪 Task 2.2: Testing Vector Similarity Setup');
  console.log('===============================================');

  const results: any[] = [];

  try {
    // Test 1: Voyage AI Service Initialization
    console.log('\n📝 Test 1: Voyage AI Service Initialization');
    
    if (!process.env.VOYAGE_API_KEY) {
      console.log('   ⚠️ VOYAGE_API_KEY not found, skipping API tests');
      results.push({
        test: 'voyage_initialization',
        success: false,
        reason: 'API key not configured'
      });
    } else {
      try {
        const embeddingService = new VoyageEmbeddingService();
        console.log('   ✅ Voyage AI service initialized successfully');
        
        results.push({
          test: 'voyage_initialization',
          success: true,
          api_key_configured: true
        });
      } catch (error) {
        console.log('   ❌ Voyage AI service initialization failed:', error);
        results.push({
          test: 'voyage_initialization',
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // Test 2: Vector Similarity Service
    console.log('\n📝 Test 2: Vector Similarity Service Creation');
    
    try {
      const vectorService = createVectorSimilarityService();
      console.log('   ✅ Vector similarity service created successfully');
      
      results.push({
        test: 'vector_service_creation',
        success: true
      });
    } catch (error) {
      console.log('   ❌ Vector similarity service creation failed:', error);
      results.push({
        test: 'vector_service_creation',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 3: Vector Utility Functions
    console.log('\n📝 Test 3: Vector Utility Functions');
    
    try {
      // Test cosine similarity
      const vector1 = [1, 0, 0];
      const vector2 = [0, 1, 0];
      const similarity = VectorUtils.cosineSimilarity(vector1, vector2);
      
      console.log(`   Cosine similarity test: ${similarity}`);
      
      // Test vector normalization
      const unnormalized = [3, 4, 0];
      const normalized = VectorUtils.normalizeVector(unnormalized);
      const norm = Math.sqrt(normalized.reduce((sum, val) => sum + val * val, 0));
      
      console.log(`   Vector normalization test: norm = ${norm.toFixed(3)} (should be 1.0)`);
      
      // Test embedding averaging
      const embeddings = [[1, 2, 3], [4, 5, 6], [7, 8, 9]];
      const averaged = VectorUtils.averageEmbeddings(embeddings);
      
      console.log(`   Embedding averaging test: [${averaged.join(', ')}]`);
      
      results.push({
        test: 'vector_utilities',
        cosine_similarity: similarity,
        normalization_success: Math.abs(norm - 1.0) < 0.001,
        averaging_success: averaged.length === 3,
        success: true
      });
      
    } catch (error) {
      console.log('   ❌ Vector utility functions failed:', error);
      results.push({
        test: 'vector_utilities',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test 4: Configuration Validation
    console.log('\n📝 Test 4: Configuration Validation');
    
    const testConfig: VectorSimilarityConfig = {
      model: 'voyage-3.5',
      dimension: 1024,
      inputType: 'query',
      truncation: true,
      batchSize: 100,
      scoreThreshold: 0.1,
      maxResults: 10
    };
    
    console.log(`   Test config: ${JSON.stringify(testConfig, null, 2)}`);
    
    results.push({
      test: 'configuration_validation',
      config: testConfig,
      success: true
    });

    console.log('\n✅ Task 2.2 Vector Similarity Tests Completed');
    return { success: true, results };

  } catch (error) {
    console.error('\n❌ Task 2.2 Tests Failed:', error);
    return { success: false, results };
  }
}

/**
 * Integration Tests: Combined Search Methods
 */
export async function testTask2Integration(): Promise<{ success: boolean; results: any[] }> {
  console.log('\n🧪 Task 2 Integration: Testing Combined Search Methods');
  console.log('=====================================================');

  const results: any[] = [];

  try {
    // Test HR-specific queries with both methods
    for (const testQuery of HR_TEST_QUERIES) {
      console.log(`\n📝 Testing Query: "${testQuery.query}"`);
      
      // Test exact matching
      const exactResults = exactWordMatch(testQuery.query, HR_TEST_RECORDS);
      const phraseResults = exactPhraseMatch(testQuery.query, HR_TEST_RECORDS);
      
      console.log(`   Exact word matches: ${exactResults.length}`);
      console.log(`   Exact phrase matches: ${phraseResults.length}`);
      
      // Check if expected records are found
      const foundIds = [...exactResults, ...phraseResults].map(r => r.record.id);
      const expectedFound = testQuery.expectedRecords.every(id => foundIds.includes(id));
      
      console.log(`   Expected records found: ${expectedFound ? '✅' : '❌'}`);
      
      results.push({
        test: 'hr_query_integration',
        query: testQuery.query,
        search_type: testQuery.searchType,
        exact_word_results: exactResults.length,
        exact_phrase_results: phraseResults.length,
        expected_found: expectedFound,
        success: exactResults.length > 0 || phraseResults.length > 0
      });
    }

    console.log('\n✅ Task 2 Integration Tests Completed');
    return { success: true, results };

  } catch (error) {
    console.error('\n❌ Task 2 Integration Tests Failed:', error);
    return { success: false, results };
  }
}

/**
 * Main test runner for Task 2
 */
export async function runTask2Tests(): Promise<boolean> {
  console.log('\n🚀 Running Task 2 Test Suite');
  console.log('============================');
  
  try {
    // Run Task 2.1 tests
    const task21Results = await testTask21ExactMatching();
    
    // Run Task 2.2 tests
    const task22Results = await testTask22VectorSimilarity();
    
    // Run integration tests
    const integrationResults = await testTask2Integration();
    
    const allTestsSucceeded = task21Results.success && task22Results.success && integrationResults.success;
    
    console.log('\n📊 Task 2 Test Summary');
    console.log('======================');
    console.log(`Task 2.1 (Exact Matching): ${task21Results.success ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`Task 2.2 (Vector Similarity): ${task22Results.success ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`Integration Tests: ${integrationResults.success ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`Overall Result: ${allTestsSucceeded ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
    
    if (allTestsSucceeded) {
      console.log('\n🎉 Ready for Task 3: Hybrid Search Engine Implementation');
    }
    
    return allTestsSucceeded;
    
  } catch (error) {
    console.error('\n❌ Task 2 Test Suite Failed:', error);
    return false;
  }
}

// Export for direct testing
export const Task2TestSuite = {
  testTask21ExactMatching,
  testTask22VectorSimilarity,
  testTask2Integration,
  runTask2Tests,
  HR_TEST_RECORDS,
  HR_TEST_QUERIES
};

// Run tests if called directly
if (require.main === module) {
  runTask2Tests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test runner failed:', error);
      process.exit(1);
    });
} 