# LLM Rules & Guidelines

**CRITICAL INSTRUCTIONS FOR FUTURE LLMs WORKING ON THIS PROJECT**

## 🚨 MANDATORY REQUIREMENTS

### 1. Package Manager - **PNPM ONLY**

- ✅ **ALWAYS use `pnpm`** - npm and yarn are **DISABLED**
- ❌ **NEVER use `npm install`** or `yarn add`
- ✅ Commands: `pnpm install`, `pnpm add`, `pnpm dev`, `pnpm dlx tsx`

```bash
# ✅ CORRECT
pnpm install
pnpm dev
pnpm dlx tsx script.ts

# ❌ WRONG - WILL FAIL
npm install
yarn install
npm run dev
```

### 2. Working Directory - **ALWAYS PROJECT ROOT**

- ✅ **Run all commands from project root**: `/Users/<USER>/Documents/CursorAI%20Projects/6.%20Taxonomy%20Match/internalKBtreev2-rd`
- ✅ **Environment file location**: `.env.local` is in **PROJECT ROOT**, not subdirectories
- ❌ **NEVER run commands from subdirectories** like `advancedSearchAPI/`

### 3. Environment Variables - **EXPLICIT LOADING REQUIRED**

- ✅ **For TSX scripts**, always load environment explicitly:

```typescript
// ✅ CORRECT - Load environment first
import { config } from "dotenv";
import { join } from "path";
config({ path: join(process.cwd(), ".env.local") });

// Then import your modules
import { syncFalkorToQdrant } from "./advancedSearchAPI/sync-service.js";
```

- ❌ **NEVER assume environment is auto-loaded** in standalone scripts

### 4. Development Server - **REQUIRED FOR API ACCESS**

- ✅ **Always run `pnpm dev`** before sync operations
- ✅ **API endpoints require Next.js server** running on localhost:3000
- ✅ **Use separate terminal** for running sync scripts while dev server runs

```bash
# Terminal 1
pnpm dev

# Terminal 2 (after server is running)
pnpm dlx tsx sync-script.ts
```

## 📊 CURRENT PROJECT STATUS

### ✅ COMPLETED & WORKING

- **Task 1.1**: Qdrant Cloud Integration (90 collections available)
- **Task 1.2**: Data Migration Pipeline (UUID + Named Vector fixes applied)
- **Task 1.3**: Base API Structure
- **Task 2.3**: SPLADE Integration (RunPod API + sparse vectors) **FULLY WORKING**
- **Task 3.1**: Hybrid Search Engine **FULLY WORKING**
- **Production Data**: `mock_hr` collection has **14 real HR documents with dense + sparse vectors** - **SEARCHABLE & WORKING**

### 🎯 PRIMARY COLLECTION - **FULLY POPULATED & WORKING** ✅

- **Collection Name**: `mock_hr`
- **Status**: ✅ POPULATED with 14 HR documents
- **Vector Config**: Named vectors with `dense` (1024 dimensions, Cosine) + `sparse` (SPLADE, Dot product)
- **Dense Vectors**: Voyage AI voyage-3.5 embeddings (1024 dimensions)
- **Sparse Vectors**: SPLADE v3 vectors via RunPod API (converted from dense to sparse format)
- **Content**: Real HR policies, onboarding procedures, offer letter components
- **Search Status**: ✅ **HYBRID SEARCH WORKING** - returning real results with both vector types!

### 📋 READY FOR DEVELOPMENT

- Task 2.1: Exact Matching Implementation
- Task 2.2: Vector Similarity Setup
- Task 3.2: Snippet Extraction
- Task 4.1: Comprehensive Testing

## 🔧 CRITICAL FIXES APPLIED

### 1. UUID Point ID Resolution ✅

**Issue**: Qdrant requires UUIDs/integers, not strings like "node-1"  
**Solution**: Deterministic MD5-based UUID generation

```typescript
// ✅ CORRECT - Deterministic UUID
const hash = createHash("md5").update(originalId).digest("hex");
const uuid = `${hash.slice(0, 8)}-${hash.slice(8, 12)}-${hash.slice(
  12,
  16
)}-${hash.slice(16, 20)}-${hash.slice(20, 32)}`;
```

### 2. Named Vector Configuration ✅ **NEW FIX**

**Issue**: Qdrant Query API requires named vectors for modern features  
**Solution**: Updated collection creation and vector storage

```typescript
// ✅ CORRECT - Named vector configuration
const collectionConfig = {
  vectors: {
    dense: {
      size: 1024,
      distance: 'Cosine'
    }
  }
};

// ✅ CORRECT - Named vector storage
const point = {
  id: deterministicUUID,
  vector: {
    dense: embedding  // Named vector format
  },
  payload: { ... }
};
```

### 3. Hybrid Search API Integration ✅ **NEW FIX**

**Issue**: "Vector params for dense are not specified in config" API errors  
**Solution**: Updated search queries to use named vectors

```typescript
// ✅ CORRECT - Named vector search
prefetches.push({
  query: queryEmbedding,
  using: "dense", // Specify named vector
  limit: vectorLimit,
  filter: filter,
});
```

### 4. Sync Status API ✅

**Issue**: Looking for `result.points_count` but API returns `points_count` directly  
**Solution**: Access points_count from root object

```typescript
// ✅ CORRECT
pointCount: collectionInfo.points_count;

// ❌ WRONG
pointCount: collectionInfo.result.points_count;
```

### 5. Upsert Status Validation ✅

**Issue**: Checking for `status === 'ok'` but Qdrant returns `'completed'`  
**Solution**: Accept both completion states

```typescript
// ✅ CORRECT
if (upsertResponse.status !== 'completed' && upsertResponse.status !== 'acknowledged')

// ❌ WRONG
if (upsertResponse.status !== 'ok')
```

### 6. Hybrid Collection Configuration ✅ **NEW - SPLADE**

**Issue**: Qdrant sparse vector configuration syntax errors  
**Solution**: Use correct `sparse_vectors` config with empty object for defaults

```typescript
// ✅ CORRECT - Hybrid collection with dense + sparse vectors
const collectionConfig = {
  vectors: {
    dense: { size: 1024, distance: "Cosine" },
  },
  sparse_vectors: {
    sparse: {}, // Empty config uses dot product distance by default
  },
};

// ❌ WRONG - Invalid sparse vector config
sparse_vectors: {
  sparse: {
    index: {
      type: "keyword";
    } // Invalid syntax
  }
}
```

### 7. SPLADE Vector Format Conversion ✅ **NEW - RunPod**

**Issue**: RunPod returns dense arrays but SPLADE needs sparse format  
**Solution**: Convert dense arrays to sparse vector format using threshold

```typescript
// ✅ CORRECT - Dense to sparse conversion
function convertDenseToSparse(
  denseVector: number[],
  threshold: number = 0.0
): SparseVector {
  const indices: number[] = [];
  const values: number[] = [];

  denseVector.forEach((value, index) => {
    if (Math.abs(value) > threshold) {
      indices.push(index);
      values.push(value);
    }
  });

  return { indices, values };
}
```

### 8. Qdrant Manual Fusion Query Parameter Issues ✅ **NEW - January 2025**

**Issue**: Manual weighted fusion returning 0 scores due to incorrect API parameter structure  
**Root Cause**: Using deprecated search API parameter structure with modern query API  
**Solution**: Updated parameter names and structure for Qdrant query API

```typescript
// ✅ CORRECT - Query API parameter structure
const searchParams = {
  query: prefetch.query, // Use 'query' parameter (not 'vector')
  using: "dense", // Named vector specification
  limit: 30,
  with_payload: true,
  with_vectors: false, // Correct parameter name (not 'with_vector')
};

// Query the collection correctly
const response = await client.query(collection_name, searchParams);
const results = response.points || []; // Handle response structure

// ❌ WRONG - Old search API structure
const searchParams = {
  vector: prefetch.query, // Deprecated parameter name
  using: "dense",
  limit: 30,
  with_payload: true,
  with_vector: false, // Missing 's' in parameter name
};
```

**Key Learnings**:

- Qdrant JS client uses `query` parameter, not `vector` parameter
- Parameter is `with_vector` (singular), not `with_vectors` (plural) - **CORRECTED**
- Always verify API parameters against official documentation when getting 0 results
- Query API is current, search API is deprecated but may appear in older examples

**UPDATE**: Initial fix incorrectly changed to `with_vectors` but official documentation confirms it should be `with_vector` (singular).

### 10. Manual Fusion Testing Results ✅ **CONFIRMED WORKING - January 2025**

**Status**: Manual weighted fusion is functioning correctly after fixes.

**Test Results Evidence**:

```
🎛️ Using manual weighted fusion (Semantic: 0.9, Keyword: 0.1)
  ✅ semantic: 14 results
  ✅ keyword: 14 results
  📊 Normalized weights: { semantic: 0.9, keyword: 0.1 }
  📈 semantic: raw scores 0.260 to 0.490
  📈 keyword: raw scores 0.742 to 0.819
    ✅ Manual fusion used: true
    ✅ Individual scores available: true
    📊 Top result contributions: {
  semantic: 1,
  keyword: 0.6139022529774357,
  weighted_fusion: 0.9613902252977435
}
```

**Key Confirmations**:

1. **✅ Automatic Method Selection**: Code correctly chooses manual fusion when weight difference > 0.1
2. **✅ Individual Score Tracking**: Both semantic and keyword scores are preserved and exposed
3. **✅ Min-Max Normalization**: Raw scores properly normalized to 0-1 range before weighting
4. **✅ Weighted Combination**: Math is correct (0.9 × 1.0 + 0.1 × 0.614 = 0.961)
5. **✅ API Parameter Structure**: `client.query(collection_name, searchParams)` works correctly
6. **✅ Named Vector Usage**: Both `using: 'dense'` and `using: 'sparse'` function properly

**Fixes That Resolved The Issues**:

- Changed `searchParams.vector` to `searchParams.query` (correct Qdrant API parameter)
- Maintained `with_vector: false` (singular, as per official documentation)
- Ensured collection name is first parameter in `client.query()`
- Proper sparse vector format: `{ indices: [], values: [] }`

**Performance**: Manual fusion adds ~50-100ms execution time but provides accurate point-level semantic vs keyword weighting.

### 11. Exact Phrase Search Filter Bypass Bug ✅ **FIXED - January 2025**

**Issue**: Exact phrase search was building proper Qdrant text filters but then bypassing them completely, doing manual verification instead.

**Root Cause**: Code was:

1. Building correct Qdrant text filter: `{ key: field, match: { text: "phrase" } }`
2. Then ignoring the filter: `client.scroll(collection, { limit, with_payload: true })` (no filter applied!)
3. Manually filtering ALL records client-side instead of trusting Qdrant

**Problems This Caused**:

- **Inefficient**: Downloaded all data instead of letting Qdrant filter server-side
- **Defeating purpose**: Not leveraging Qdrant's full-text indexing capabilities
- **Causing 0 results**: Manual verification logic differed from Qdrant's matching
- **Poor performance**: Processing all records instead of filtered subset

**Solution**: Trust Qdrant's text filtering and actually use it:

```typescript
// ❌ WRONG - Build filter but don't use it
const searchResponse = await client.scroll(collection_name, {
  limit: limit * 10,
  with_payload: true,
  with_vector: false,
  // Missing: filter: filter
});

// ✅ CORRECT - Use the filter we built
const searchResponse = await client.scroll(collection_name, {
  limit: limit * 2,
  filter: filter, // Actually apply the text filter!
  with_payload: true,
  with_vector: false,
});
```

**Key Learning**: If you have a database with full-text indexing (like Qdrant), trust it to do its job instead of doing redundant client-side filtering that defeats the purpose of the database.

### 9. Qdrant Query API Collection Parameter ✅ **NEW - January 2025**

**Issue**: Manual fusion was calling `client.query(searchParams)` with only one parameter, but Qdrant Query API requires collection name as first parameter.

**Root Cause**: Misunderstanding of Qdrant Query API signature from documentation.

**Solution**: Based on [official Qdrant documentation](https://qdrant.tech/documentation/concepts/search/), the correct API call is:

```typescript
// ❌ WRONG - Missing collection name parameter
const response = await client.query(searchParams);

// ✅ CORRECT - Collection name as first parameter
const response = await client.query(collection_name, searchParams);
```

**Key Learning**: Always follow official API documentation exactly. The Qdrant JS client.query() method signature is `client.query(collection_name, options)` where:

- First parameter: collection name (string)
- Second parameter: query options object

**Fix Applied**: Updated manual weighted fusion to use correct API signature.  
 **Solution**: Convert dense to sparse by extracting non-zero values

```typescript
// ✅ CORRECT - Convert RunPod dense response to sparse format
const denseEmbedding = data.output.data[0].embedding;
const sparseVector = { indices: [], values: [] };

denseEmbedding.forEach((value, index) => {
  if (Math.abs(value) > 1e-8) {
    // Threshold for non-zero
    sparseVector.indices.push(index);
    sparseVector.values.push(value);
  }
});

// ❌ WRONG - Using dense array directly as sparse
const sparseVector = data.output.data[0].embedding; // Wrong format
```

### 8. Environment Loading for Scripts ✅ **CRITICAL**

**Issue**: Scripts don't inherit Next.js environment variables  
**Solution**: Explicitly load .env.local from project root

```typescript
// ✅ CORRECT - Always at top of standalone scripts
import { config } from "dotenv";
import { resolve } from "path";
config({ path: resolve(process.cwd(), ".env.local") });

// Then import modules that need environment
import { spladeService } from "./splade-service.js";
```

## 🧪 TESTING COMMANDS - **ALL WORKING** ✅

### Working Test Commands ✅

```bash
# Check sync status (should show 14 points with dense + sparse vectors)
pnpm dlx tsx -e "
import { config } from 'dotenv';
import { join } from 'path';
config({ path: join(process.cwd(), '.env.local') });
import { getSyncStatus } from './advancedSearchAPI/sync-service.js';
const status = await getSyncStatus('mock_hr');
console.log(\`Points: \${status.pointCount}, Exists: \${status.collectionExists}\`);
"

# Run hybrid search tests (should return REAL search results)
pnpm dlx tsx advancedSearchAPI/test-task3-1.ts

# Run full sync test (should show SUCCESS with 14 synced including SPLADE)
pnpm dlx tsx advancedSearchAPI/test-task1-2.ts

# Test SPLADE integration specifically
pnpm dlx tsx advancedSearchAPI/test-task2-3.ts
```

### Test Files Available

- `test-task1.ts` - Qdrant integration tests
- `test-task1-2.ts` - Data migration tests
- `test-task3-1.ts` - Hybrid search tests **RETURNING REAL RESULTS**

### **VERIFIED WORKING SEARCH RESULTS** ✅

Recent test results show **REAL SEARCHES WORKING**:

```bash
📝 Testing: "employee onboarding process"
   ✅ Results: 5 found
   🎯 Top result: "A Deep Dive into Form I-9 Compliance"
   📊 Score: 0.5000

📝 Testing: "payroll tax compliance requirements"
   ✅ Results: 5 found
   🎯 Top result: "What is an Off-Cycle Payroll?"
   📊 Score: 0.6982
```

## 🚨 COMMON PITFALLS TO AVOID

### ❌ DON'T DO THESE:

1. **Run npm/yarn commands** (will fail due to package.json restrictions)
2. **Run scripts from subdirectories** (wrong environment path)
3. **Forget to start dev server** before API-dependent operations
4. **Assume environment auto-loads** in standalone scripts
5. **Use string IDs for Qdrant points** (must be UUIDs)
6. **Check for 'ok' status** from Qdrant (returns 'completed')
7. **Access result.points_count** (API returns points_count directly)
8. **Use single vector format** (must use named vectors: `{ dense: embedding }`)
9. **Skip 'using' parameter** in search (must specify `using: 'dense'`)
10. **Use invalid sparse vector config** (don't add `index` properties to sparse_vectors)
11. **Use dense arrays as sparse vectors** (must convert to indices/values format)
12. **Forget environment loading** in standalone scripts (RunPod/Qdrant APIs fail without keys)

### ✅ ALWAYS DO THESE:

1. **Use pnpm for all operations**
2. **Run commands from project root**
3. **Load .env.local explicitly in scripts** (critical for RunPod/Qdrant APIs)
4. **Start dev server before sync operations**
5. **Use deterministic UUIDs for Qdrant point IDs**
6. **Check for 'completed'/'acknowledged' statuses**
7. **Access points_count from root API response**
8. **Use named vector format: `{ dense: embedding }`**
9. **Specify `using: 'dense'` in search queries**
10. **Use empty config for sparse vectors: `sparse_vectors: { sparse: {} }`**
11. **Convert RunPod dense arrays to sparse format (indices/values)**
12. **Test with mock_hr collection (has real data + both vector types for verification)**

## 📁 PROJECT STRUCTURE

```
internalKBtreev2-rd/                 # ← PROJECT ROOT (work from here)
├── .env.local                       # ← Environment variables HERE
├── package.json                     # ← pnpm config, npm/yarn disabled
├── .npmrc                          # ← Enforces pnpm usage
├── advancedSearchAPI/              # ← Advanced search implementation
│   ├── hybrid-search.ts            # ← Task 3.1 COMPLETED & WORKING
│   ├── sync-service.ts             # ← UUID + Named Vector fixes applied
│   ├── qdrant-client.ts            # ← Named Vector compatibility applied
│   ├── test-task3-1.ts             # ← Working test suite REAL RESULTS
│   └── README.md                   # ← Updated documentation
├── app/api/                        # ← Next.js API routes
└── components/                     # ← UI components
```

## 🔗 API ENDPOINTS

### Working Endpoints ✅

- **GET/POST** `/api/search/advanced` - Advanced search API **WORKING WITH REAL DATA**
- **GET** `/api/data-falkor` - FalkorDB data with pagination
- **GET** `/api/falkor/nodes` - Graph node operations

### Required Parameters

- **graph_name**: Use `"mock_hr"` as default (populated collection)
- **pagination**: `?page=0&limit=1000` for data-falkor
- **Content-Type**: `application/json` for POST requests

## 🎯 DEVELOPMENT WORKFLOW

### For New Features:

1. **Start dev server**: `pnpm dev`
2. **Test existing functionality** with populated `mock_hr` collection (14 documents with dense + sparse vectors)
3. **Use TSX for TypeScript scripts** with explicit environment loading
4. **Test incrementally** with small batches first
5. **Verify changes** don't break existing UUID/sync/named vector/SPLADE fixes

### For Data Sync Operations:

1. **Production sync**: Use `sync-service.ts` (includes SPLADE by default since Task 2.3)
2. **Backfill only**: Use `sync-with-splade.ts` (for pre-SPLADE collections)
3. **Default behavior**: All new syncs include both dense + sparse vectors automatically
4. **No manual SPLADE setup needed**: Main sync service handles both vector types

### For Debugging:

1. **Check collection status** first: run getSyncStatus test (should show 14 points)
2. **Verify dev server** is running for API access
3. **Check environment loading** in scripts
4. **Use explicit error logging** in try/catch blocks
5. **Test with mock_hr collection** (has real data - searches return results)

## 📋 TASK PRIORITIES

### HIGH PRIORITY (Next to implement):

1. **Task 2.1**: Exact Matching Implementation
2. **Task 2.2**: Vector Similarity Setup

### MEDIUM PRIORITY:

1. **Task 2.3**: SPLADE Integration
2. **Task 3.2**: Snippet Extraction

### LOW PRIORITY:

1. **Task 4.1**: Comprehensive Testing (infrastructure working + data populated)

## 💡 IMPLEMENTATION HINTS

### When working with Qdrant:

- **Point IDs**: Always generate deterministic UUIDs from original IDs
- **Collections**: Default to `mock_hr` (populated and tested with 14 documents)
- **Dense Embeddings**: Use voyage-3.5 model (1024 dimensions, Cosine similarity)
- **Sparse Vectors**: Use SPLADE via RunPod API (converted to indices/values format)
- **Hybrid Collections**: Use `sparse_vectors: { sparse: {} }` config (empty = defaults)
- **Named Vectors**: ALWAYS use `{ dense: embedding, sparse_vector: { sparse: sparseVector } }`
- **Search Queries**: ALWAYS specify `using: 'dense'` or `using: 'sparse'` parameter

### When working with FalkorDB:

- **Graph Name**: `mock_hr` is the primary graph with real data
- **API Access**: Requires Next.js dev server running
- **Pagination**: Use reasonable batch sizes (10-50 for testing)

### When working with Search:

- **Collection**: `mock_hr` has 14 real HR documents for testing
- **Dense Vectors**: 1024-dimensional Voyage AI embeddings are populated
- **Sparse Vectors**: SPLADE vectors converted from RunPod dense responses
- **Content**: Real HR policies, procedures, onboarding guides
- **Search Results**: VERIFIED working - returns real documents with scores
- **Fusion Methods**: RRF and DBSF both working in hybrid search
- **Vector Types**: Both dense (semantic) and sparse (keyword-based) available

---

## 🎯 SUMMARY FOR FUTURE LLMs

**YOU ARE WORKING ON**: An advanced search system with Qdrant Cloud + FalkorDB integration

**CURRENT STATUS**: Core infrastructure ✅ COMPLETE, hybrid search ✅ WORKING WITH REAL DATA, SPLADE integration ✅ COMPLETE, 14 HR documents ✅ POPULATED AND SEARCHABLE WITH BOTH VECTOR TYPES

**YOUR TASK**: Implement remaining search methods (exact matching, vector similarity)

**CRITICAL**: Use pnpm, work from root, load environment explicitly, use named vectors, test with mock_hr collection

**VERIFIED WORKING**: 14 HR documents with dense + sparse vectors returning real search results, UUID + Named Vector + SPLADE fixes applied, full hybrid search infrastructure complete and operational

**NEXT STEPS**: Focus on Task 2.1 (Exact Matching) using the populated mock_hr collection with both vector types as your test target

**SEARCH VERIFICATION**: Recent tests confirm queries return real results with proper scoring, SPLADE integration working with RunPod API, hybrid collections operational
