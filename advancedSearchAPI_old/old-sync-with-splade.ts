/**
 * LEGACY BACKFILL SCRIPT: Sync FalkorDB data to Qdrant with SPLADE sparse vectors
 * 
 * ⚠️  NOTE: Since Task 2.3 completion, the main sync-service.ts includes SPLADE 
 *          integration by default. This script is only needed for backfilling 
 *          existing collections that were created before SPLADE integration.
 * 
 * For new installations, use the standard sync process:
 * - sync-service.ts automatically includes both dense + sparse vectors
 * - test-task1-2.ts runs the standard sync with SPLADE included
 * 
 * This script re-syncs existing collections to include both:
 * - Dense vectors (Voyage AI embeddings) 
 * - Sparse vectors (SPLADE via RunPod)
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Load environment variables from .env.local in the project root
config({ path: resolve(process.cwd(), '.env.local') });

import { syncFalkorToQdrant, getSyncStatus } from './sync-service';
import { testSpladeService } from './splade-service';

async function main() {
  console.log('🔄 Re-syncing mock_hr collection with SPLADE integration');
  console.log('====================================================\n');

  // Step 1: Test SPLADE service availability
  console.log('🧪 Step 1: Testing SPLADE service availability');
  console.log('----------------------------------------------');
  
  try {
    const spladeAvailable = await testSpladeService();
    if (!spladeAvailable) {
      console.error('❌ SPLADE service is not available!');
      console.log('\n🔧 Please check:');
      console.log('   - RUNPOD_API_KEY environment variable');
      console.log('   - RUNPOD_ENDPOINT_ID environment variable');
      console.log('   - RunPod endpoint is running and accessible');
      process.exit(1);
    }
    console.log('✅ SPLADE service is available and ready');
  } catch (error) {
    console.error('❌ Error testing SPLADE service:', error);
    process.exit(1);
  }

  console.log('');

  // Step 2: Check current collection status
  console.log('📊 Step 2: Checking current collection status');
  console.log('---------------------------------------------');
  
  try {
    const currentStatus = await getSyncStatus('mock_hr');
    console.log(`Current status: ${currentStatus.pointCount} points in mock_hr collection`);
    
    if (currentStatus.pointCount > 0) {
      console.log('⚠️  Collection will be recreated with sparse vector support');
      console.log('   All existing data will be re-processed with SPLADE vectors');
    } else {
      console.log('📝 Collection is empty, will populate with dense + sparse vectors');
    }
  } catch (error) {
    console.error('❌ Error checking collection status:', error);
    process.exit(1);
  }

  console.log('');

  // Step 3: Sync with SPLADE integration
  console.log('🚀 Step 3: Syncing with SPLADE integration');
  console.log('------------------------------------------');
  
  try {
    const syncResult = await syncFalkorToQdrant('mock_hr', {
      apiBaseUrl: 'http://localhost:3000',
      batchSize: 10, // Smaller batches for SPLADE processing
      forceRecreate: true, // Force recreate to add sparse vector support
      skipExisting: false
    });

    if (syncResult.success) {
      console.log('✅ Sync completed successfully!');
      console.log(`   Total processed: ${syncResult.totalProcessed}`);
      console.log(`   Total synced: ${syncResult.totalSynced}`);
      console.log(`   Duration: ${syncResult.duration}ms`);
      
      if (syncResult.errors.length > 0) {
        console.log(`   Errors: ${syncResult.errors.length}`);
        syncResult.errors.forEach(error => console.log(`     - ${error}`));
      }
    } else {
      console.error('❌ Sync failed!');
      console.log(`   Message: ${syncResult.message}`);
      console.log(`   Errors: ${syncResult.errors.join(', ')}`);
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Error during sync:', error);
    process.exit(1);
  }

  console.log('');

  // Step 4: Verify final collection status
  console.log('✅ Step 4: Verifying final collection status');
  console.log('--------------------------------------------');
  
  try {
    const finalStatus = await getSyncStatus('mock_hr');
    console.log(`Final status: ${finalStatus.pointCount} points in mock_hr collection`);
    
    if (finalStatus.pointCount > 0) {
      console.log('🎉 Collection successfully updated with dense + sparse vectors!');
      console.log('\n📋 Ready for hybrid search testing:');
      console.log('   - Dense vectors: Voyage AI embeddings (semantic search)');
      console.log('   - Sparse vectors: SPLADE vectors (keyword matching)');
      console.log('   - Collection: mock_hr with multi-vector support');
      
      console.log('\n🔧 Next steps:');
      console.log('   1. Test hybrid search: pnpm dlx tsx advancedSearchAPI/test-task3-1.ts');
      console.log('   2. Update search API to use sparse vectors');
      console.log('   3. Test full integration with frontend');
    } else {
      console.error('⚠️  Warning: Collection appears to be empty after sync');
    }
  } catch (error) {
    console.error('❌ Error verifying final status:', error);
  }

  console.log('\n🎯 SPLADE Integration Complete!');
}

// Run the sync
main().catch(error => {
  console.error('\n💥 Sync failed with error:', error);
  process.exit(1);
}); 