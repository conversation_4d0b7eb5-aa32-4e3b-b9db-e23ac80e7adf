/**
 * Task 1.3: Base API Structure - Test Suite
 * 
 * Tests for the /api/search/advanced endpoint including:
 * - Request validation middleware
 * - Response formatting utilities
 * - Error handling and logging
 * - HR-specific filters
 * - Graph name parameter support
 */

import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config({ path: path.join(process.cwd(), '.env.local') });

/**
 * Test the API endpoint functionality
 */
export async function testTask1_3() {
  console.log('🧪 Testing Task 1.3: Base API Structure');
  console.log('=========================================');
  
  const baseUrl = 'http://localhost:3000';
  let allTestsPassed = true;

  // Test 1: GET endpoint documentation
  try {
    console.log('\n📖 Test 1: GET endpoint documentation');
    const response = await fetch(`${baseUrl}/api/search/advanced`);
    const data = await response.json();
    
    if (data.task_status?.includes('COMPLETED') && data.example_request) {
      console.log('✅ GET documentation endpoint working');
      console.log(`   Status: ${data.task_status}`);
    } else {
      console.log('❌ GET documentation endpoint failed');
      allTestsPassed = false;
    }
  } catch (error) {
    console.log('❌ GET endpoint test failed:', error);
    allTestsPassed = false;
  }

  // Test 2: Valid POST request
  try {
    console.log('\n✅ Test 2: Valid POST request');
    const validRequest = {
      query: 'employee onboarding best practices',
      graph_name: 'mock_hr',
      search_methods: {
        vector_similarity: { enabled: true, weight: 0.7 },
        exact_phrase: { enabled: true, weight: 0.3 }
      },
      filters: {
        section_path: ['People Management'],
        article_type: 'article'
      },
      result_options: {
        limit: 5,
        include_snippets: true,
        snippet_context_words: 10
      }
    };

    const response = await fetch(`${baseUrl}/api/search/advanced`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(validRequest)
    });

    const data = await response.json();
    
    if (data.success && data.query === validRequest.query && data.results) {
      console.log('✅ Valid POST request successful');
      console.log(`   Query: "${data.query}"`);
      console.log(`   Graph: ${data.graph_name}`);
      console.log(`   Results: ${data.total_results}`);
      console.log(`   Execution time: ${data.execution_time_ms}ms`);
    } else {
      console.log('❌ Valid POST request failed');
      console.log('   Response:', data);
      allTestsPassed = false;
    }
  } catch (error) {
    console.log('❌ Valid POST request test failed:', error);
    allTestsPassed = false;
  }

  // Test 3: Request validation
  try {
    console.log('\n🔍 Test 3: Request validation');
    const invalidRequest = {
      query: '', // Empty query
      graph_name: null, // Missing graph_name
      search_methods: {
        invalid_method: { enabled: true, weight: 1.5 } // Invalid method and weight
      },
      filters: {
        article_type: 'invalid_type', // Invalid article type
        level: -1 // Negative level
      },
      result_options: {
        limit: 150 // Exceeds max limit
      }
    };

    const response = await fetch(`${baseUrl}/api/search/advanced`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(invalidRequest)
    });

    const data = await response.json();
    
    if (!data.success && data.validation_errors && data.validation_errors.length > 0) {
      console.log('✅ Request validation working');
      console.log(`   Validation errors found: ${data.validation_errors.length}`);
      data.validation_errors.forEach((error: string, index: number) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    } else {
      console.log('❌ Request validation failed');
      console.log('   Expected validation errors, got:', data);
      allTestsPassed = false;
    }
  } catch (error) {
    console.log('❌ Request validation test failed:', error);
    allTestsPassed = false;
  }

  // Test 4: HR-specific filters
  try {
    console.log('\n🏢 Test 4: HR-specific filters');
    const filterTests = [
      {
        name: 'Section path filter',
        filters: { section_path: ['People Management', 'Hiring'] }
      },
      {
        name: 'Article type filter',
        filters: { article_type: 'section' }
      },
      {
        name: 'Level filter',
        filters: { level: 3 }
      },
      {
        name: 'Combined filters',
        filters: { 
          section_path: ['People Management'], 
          article_type: 'article',
          level: 2
        }
      }
    ];

    for (const test of filterTests) {
      const request = {
        query: 'test query',
        graph_name: 'mock_hr',
        filters: test.filters
      };

      const response = await fetch(`${baseUrl}/api/search/advanced`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request)
      });

      const data = await response.json();
      
      if (data.success) {
        console.log(`   ✅ ${test.name}: ${data.total_results} results`);
      } else {
        console.log(`   ❌ ${test.name} failed:`, data.error);
        allTestsPassed = false;
      }
    }
  } catch (error) {
    console.log('❌ HR-specific filters test failed:', error);
    allTestsPassed = false;
  }

  // Test 5: Response structure validation
  try {
    console.log('\n📋 Test 5: Response structure validation');
    const request = {
      query: 'test response structure',
      graph_name: 'mock_hr',
      search_methods: {
        vector_similarity: { enabled: true, weight: 1.0 }
      },
      result_options: {
        include_snippets: true
      }
    };

    const response = await fetch(`${baseUrl}/api/search/advanced`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request)
    });

    const data = await response.json();
    
    // Validate response structure
    const requiredFields = ['success', 'query', 'graph_name', 'collection_name', 'total_results', 'execution_time_ms', 'results'];
    const missingFields = requiredFields.filter(field => !(field in data));
    
    if (missingFields.length === 0) {
      console.log('✅ Response structure is valid');
      
      // Check results structure
      if (data.results && data.results.length > 0) {
        const result = data.results[0];
        const resultFields = ['record', 'relevance_score', 'method_contributions'];
        const missingResultFields = resultFields.filter(field => !(field in result));
        
        if (missingResultFields.length === 0) {
          console.log('✅ Result structure is valid');
          
          // Check if snippets are included when requested
          if (result.snippets && result.snippets.length > 0) {
            console.log('✅ Snippets included when requested');
          } else {
            console.log('⚠️ Snippets not included (may be expected)');
          }
        } else {
          console.log('❌ Result structure missing fields:', missingResultFields);
          allTestsPassed = false;
        }
      }
    } else {
      console.log('❌ Response structure missing fields:', missingFields);
      allTestsPassed = false;
    }
  } catch (error) {
    console.log('❌ Response structure validation test failed:', error);
    allTestsPassed = false;
  }

  // Test 6: Error handling
  try {
    console.log('\n⚠️ Test 6: Error handling');
    
    // Test with malformed JSON
    const response = await fetch(`${baseUrl}/api/search/advanced`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: 'invalid json'
    });

    const data = await response.json();
    
    if (!data.success && data.error) {
      console.log('✅ Error handling working for malformed JSON');
      console.log(`   Error: ${data.error}`);
    } else {
      console.log('❌ Error handling failed for malformed JSON');
      allTestsPassed = false;
    }
  } catch (error) {
    console.log('❌ Error handling test failed:', error);
    allTestsPassed = false;
  }

  // Summary
  console.log('\n📊 Task 1.3 Test Summary');
  console.log('========================');
  
  if (allTestsPassed) {
    console.log('🎉 All tests passed! Task 1.3: Base API Structure is working correctly');
    console.log('\n✅ Completed features:');
    console.log('   • /api/search/advanced endpoint created');
    console.log('   • Request validation middleware implemented');
    console.log('   • Response formatting utilities working');
    console.log('   • HR-specific filters functional');
    console.log('   • Error handling and logging in place');
    console.log('   • Graph name parameter support confirmed');
    console.log('\n🚀 Ready for Phase 2: Core Search Methods (Tasks 2.1-2.3)');
  } else {
    console.log('❌ Some tests failed. Please review the implementation.');
  }

  return allTestsPassed;
}

/**
 * Performance test for the API endpoint
 */
export async function testTask1_3Performance() {
  console.log('\n⚡ Performance Test: API Response Times');
  console.log('=====================================');

  const baseUrl = 'http://localhost:3000';
  const testQueries = [
    'employee onboarding',
    'payroll tax requirements',
    'hiring best practices',
    'form i-9 compliance',
    'international contractors'
  ];

  const times: number[] = [];

  for (const query of testQueries) {
    const startTime = Date.now();
    
    try {
      const response = await fetch(`${baseUrl}/api/search/advanced`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query,
          graph_name: 'mock_hr',
          search_methods: {
            vector_similarity: { enabled: true, weight: 0.8 }
          }
        })
      });

      const data = await response.json();
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      times.push(responseTime);
      console.log(`   Query: "${query}" - ${responseTime}ms (API reported: ${data.execution_time_ms}ms)`);
      
    } catch (error) {
      console.log(`   Query: "${query}" - FAILED`);
    }
  }

  if (times.length > 0) {
    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    const maxTime = Math.max(...times);
    const minTime = Math.min(...times);
    
    console.log(`\n📊 Performance Results:`);
    console.log(`   Average response time: ${avgTime.toFixed(2)}ms`);
    console.log(`   Fastest response: ${minTime}ms`);
    console.log(`   Slowest response: ${maxTime}ms`);
    
    if (avgTime < 500) {
      console.log('✅ Performance target met (<500ms average)');
    } else {
      console.log('⚠️ Performance target not met (>500ms average)');
    }
  }
}

/**
 * Run all Task 1.3 tests
 */
export async function runAllTask1_3Tests() {
  console.log('🧪 Task 1.3: Base API Structure - Complete Test Suite');
  console.log('====================================================');
  
  const functionalTests = await testTask1_3();
  await testTask1_3Performance();
  
  return functionalTests;
}

// Export individual test functions
export {
  testTask1_3 as testApiStructure,
  testTask1_3Performance as testApiPerformance,
};

// Run tests if called directly
if (require.main === module) {
  runAllTask1_3Tests().then(success => {
    process.exit(success ? 0 : 1);
  });
} 