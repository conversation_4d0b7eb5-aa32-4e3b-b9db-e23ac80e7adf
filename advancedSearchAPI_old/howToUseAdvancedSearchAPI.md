# Advanced Search API Documentation

## Overview

The Advanced Search API provides sophisticated search capabilities across HR knowledge bases using multiple search methods including semantic similarity, keyword matching, exact phrase search, and hybrid fusion techniques.

**API Endpoint:** `POST /api/search/advanced`

## Search Methods Available

### 1. Vector Similarity Search (Semantic Search)

Uses dense vector embeddings to find semantically similar content.

```typescript
{
  "search_methods": {
    "vector_similarity": {
      "enabled": true,
      "weight": 0.7,
      "model": "voyage-3.5",
      "dimension": 1024
    }
  }
}
```

**Features:**

- Semantic understanding of queries
- Finds related concepts even with different wording
- Uses Voyage AI embeddings (voyage-3.5 model)
- Supports 256, 512, 1024, or 2048 dimensions

### 2. Sparse Vector Search (Keyword-Based)

Uses SPLADE sparse vectors for precise keyword matching.

```typescript
{
  "search_methods": {
    "sparse_vectors": {
      "enabled": true,
      "weight": 0.4
    }
  }
}
```

**Features:**

- Precise keyword matching
- Term importance weighting
- Handles synonyms and related terms
- Complementary to semantic search

### 3. Exact Phrase Search

Finds exact phrase matches within content using Qdrant's full-text search.

```typescript
{
  "search_methods": {
    "exact_phrase": {
      "enabled": true,
      "weight": 0.3,
      "options": {
        "search_fields": ["title", "content", "summary", "fullContent"],
        "entity_types": ["both"],
        "case_sensitive": false,
        "whole_words_only": false
      }
    }
  }
}
```

**Features:**

- Exact phrase matching with quotes
- Field-specific search (title, content, summary, fullContent)
- Entity type filtering (nodes, articles, or both)
- Case sensitivity options
- Whole word matching options

**Hybrid Search Behavior:**
When combined with other search methods, exact phrase search uses **OR logic** (union), not AND:

- Returns documents that contain the exact phrase **OR** are semantically/keyword related
- Provides broader coverage, not narrower filtering
- Each method contributes independently to the final ranking

### 4. Exact Word Search

Finds documents containing specific words or terms.

```typescript
{
  "search_methods": {
    "exact_words": {
      "enabled": true,
      "weight": 0.2
    }
  }
}
```

**Features:**

- Individual word matching
- Boolean AND/OR logic
- Term frequency scoring

## How Search Methods Combine

### Search Method Logic: OR (Union), Not AND (Intersection)

When multiple search methods are enabled, they work together using **OR logic** to provide comprehensive coverage:

```typescript
// Example: Semantic + Exact Phrase
{
  "query": "employee benefits",
  "search_methods": {
    "vector_similarity": { "enabled": true, "weight": 0.7 },
    "exact_phrase": { "enabled": true, "weight": 0.3 }
  }
}
```

**How it works:**

1. **Semantic search** finds: "compensation packages", "workplace perks", "health insurance"
2. **Exact phrase search** finds: documents containing literal "employee benefits"
3. **Fusion combines both** using weighted scoring
4. **Result**: All relevant documents (exact matches + semantic matches)

### Result Composition

| Search Method       | Finds                        | Example Results                                |
| ------------------- | ---------------------------- | ---------------------------------------------- |
| **Semantic**        | Conceptually related content | "compensation packages", "workplace perks"     |
| **Exact Phrase**    | Literal phrase matches       | Documents with "employee benefits"             |
| **Sparse/Keyword**  | Term-based matches           | "employee", "benefits", related keywords       |
| **Combined Result** | Union of all methods         | Comprehensive coverage with weighted relevance |

### Method Contribution Tracking

Each result shows how different methods contributed:

```json
{
  "relevance_score": 0.85,
  "method_contributions": {
    "semantic": 0.92, // High semantic relevance
    "exact_phrase": 1.0, // Perfect phrase match
    "weighted_fusion": 0.85 // Combined weighted score
  }
}
```

**Benefits of OR Logic:**

- ✅ **Comprehensive coverage** - doesn't miss relevant content
- ✅ **Flexible ranking** - exact matches can rank higher
- ✅ **Semantic expansion** - finds related concepts
- ✅ **Precision control** - adjust weights for different behaviors

## Fusion Methods

### 1. Reciprocal Rank Fusion (RRF) - Default

Combines results from multiple search methods using rank-based scoring.

```typescript
{
  "fusion": {
    "method": { "type": "rrf", "params": { "k": 2 } },
    "enabled": true
  }
}
```

**Best for:** Balanced weights between search methods

### 2. Distribution-Based Score Fusion (DBSF)

Advanced fusion method that normalizes score distributions.

```typescript
{
  "fusion": {
    "method": { "type": "dbsf" },
    "enabled": true
  }
}
```

**Best for:** When search methods have different score ranges

### 3. Manual Weighted Fusion

Provides precise control over individual method contributions.

```typescript
{
  "search_methods": {
    "vector_similarity": { "enabled": true, "weight": 0.9 },
    "sparse_vectors": { "enabled": true, "weight": 0.1 }
  }
}
```

**Features:**

- True score-level weighting (e.g., 90% semantic, 10% keyword)
- Individual method score tracking
- Automatically triggered when weight difference > 0.1

### 4. Multi-Stage Search

Hierarchical search with progressive refinement.

```typescript
{
  "multi_stage": {
    "enabled": true,
    "stages": [
      { "prefetch_limit": 1000, "using": "dense", "query_type": "dense" },
      { "prefetch_limit": 100, "using": "dense", "query_type": "dense" }
    ]
  }
}
```

**Best for:** Large datasets requiring progressive filtering

## Search Modes

### Hybrid Search (Default)

Combines multiple search methods using fusion techniques.

```typescript
{
  "query": "employee benefits package",
  "search_methods": {
    "vector_similarity": { "enabled": true, "weight": 0.6 },
    "sparse_vectors": { "enabled": true, "weight": 0.4 }
  }
}
```

### Single Vector Mode

Search using only one specific vector type.

```typescript
{
  "query": "vacation policy",
  "single_vector_mode": {
    "enabled": true,
    "vector_type": "dense" // or "sparse"
  }
}
```

## Filtering Options

### HR-Specific Filters

Filter results by organizational structure and metadata.

```typescript
{
  "filters": {
    "section_path": ["People Management", "Benefits"],
    "article_type": "article", // or "section"
    "level": 2,
    "update_date_after": "2024-01-01"
  }
}
```

**Available Filters:**

- `section_path`: Array of section paths
- `article_type`: "section" or "article"
- `level`: Hierarchical level (number)
- `update_date_after`: ISO date string

### Entity Type Filtering

Filter by content type (for exact phrase search).

```typescript
{
  "search_methods": {
    "exact_phrase": {
      "options": {
        "entity_types": ["node"], // "node", "article", or "both"
        "search_fields": ["title", "content"]
      }
    }
  }
}
```

## Result Options

### Basic Options

```typescript
{
  "result_options": {
    "limit": 10,
    "offset": 0,
    "include_snippets": true,
    "snippet_context_words": 15
  }
}
```

### Snippet Configuration

```typescript
{
  "result_options": {
    "include_snippets": true,
    "snippet_context_words": 20 // Words before/after match
  }
}
```

## Complete Request Examples

### 1. Balanced Hybrid Search

```json
{
  "query": "remote work policy guidelines",
  "graph_name": "mock_hr",
  "search_methods": {
    "vector_similarity": { "enabled": true, "weight": 0.5 },
    "sparse_vectors": { "enabled": true, "weight": 0.5 }
  },
  "filters": {
    "section_path": ["Workplace Policies"],
    "article_type": "article"
  },
  "result_options": {
    "limit": 10,
    "include_snippets": true,
    "snippet_context_words": 15
  }
}
```

### 2. Semantic-Heavy Search

```json
{
  "query": "employee wellbeing initiatives",
  "graph_name": "mock_hr",
  "search_methods": {
    "vector_similarity": { "enabled": true, "weight": 0.9 },
    "sparse_vectors": { "enabled": true, "weight": 0.1 }
  },
  "result_options": {
    "limit": 15
  }
}
```

### 3. Exact Phrase Search

```json
{
  "query": "vacation policy",
  "graph_name": "mock_hr",
  "search_methods": {
    "exact_phrase": {
      "enabled": true,
      "weight": 1.0,
      "options": {
        "search_fields": ["title", "content", "summary"],
        "entity_types": ["both"],
        "case_sensitive": false
      }
    }
  },
  "result_options": {
    "limit": 5,
    "include_snippets": true,
    "snippet_context_words": 20
  }
}
```

### 4. Multi-Stage Advanced Search

```json
{
  "query": "performance review process",
  "graph_name": "mock_hr",
  "search_methods": {
    "vector_similarity": { "enabled": true, "weight": 0.7 }
  },
  "fusion": {
    "method": { "type": "dbsf" },
    "enabled": true
  },
  "multi_stage": {
    "enabled": true,
    "stages": [
      { "prefetch_limit": 1000, "using": "dense" },
      { "prefetch_limit": 100, "using": "dense" }
    ]
  },
  "result_options": {
    "limit": 10
  }
}
```

### 5. Single Vector Search

```json
{
  "query": "company holidays",
  "graph_name": "mock_hr",
  "single_vector_mode": {
    "enabled": true,
    "vector_type": "sparse"
  },
  "result_options": {
    "limit": 10
  }
}
```

## Response Format

### Successful Response

```json
{
  "success": true,
  "query": "employee benefits package",
  "collection_name": "mock_hr",
  "search_mode": "fusion",
  "total_results": 10,
  "execution_time_ms": 1250,
  "results": [
    {
      "record": {
        "id": "rec_123",
        "title": "Employee Benefits Overview",
        "content": "Comprehensive guide to employee benefits...",
        "summary": "Overview of all employee benefits...",
        "breadcrumb": "HR > Benefits > Overview",
        "section_path": ["HR", "Benefits"],
        "article_type": "article",
        "level": 2,
        "nodeType": "record"
      },
      "relevance_score": 0.95,
      "method_contributions": {
        "semantic": 0.92,
        "keyword": 0.78,
        "weighted_fusion": 0.95
      },
      "snippets": [
        {
          "text": "employee benefits package includes health insurance",
          "context_before": "Our comprehensive",
          "context_after": "dental coverage and retirement plans",
          "start_position": 45,
          "end_position": 89,
          "relevance_score": 1.0
        }
      ]
    }
  ],
  "fusion_info": {
    "method": {
      "type": "manual_weighted",
      "params": { "vectorWeight": 0.9, "sparseWeight": 0.1 }
    },
    "combined_results": 28,
    "unique_results": 10
  }
}
```

### Error Response

```json
{
  "success": false,
  "error": "Query is required",
  "details": "Query parameter cannot be empty",
  "processingTimeMs": 5
}
```

## Quick Start Functions

The `hybrid-search.ts` module provides convenient functions for common use cases:

### Quick Hybrid Search

```typescript
import { quickHybridSearch } from "./hybrid-search";

const results = await quickHybridSearch("employee onboarding", "mock_hr", {
  search_methods: {
    vector_similarity: { enabled: true, weight: 0.7 },
    exact_phrase: { enabled: true, weight: 0.3 },
  },
});
```

### HR-Optimized Search

```typescript
import { hrHybridSearch } from "./hybrid-search";

const results = await hrHybridSearch(
  "remote work policy",
  "mock_hr",
  { section_path: ["Workplace Policies"] },
  10
);
```

### Exact Phrase Search Functions

```typescript
import {
  exactPhraseSearchNodes,
  exactPhraseSearchArticles,
  exactPhraseSearchAll,
} from "./hybrid-search";

// Search only in nodes (categories)
const nodeResults = await exactPhraseSearchNodes("vacation policy");

// Search only in articles
const articleResults = await exactPhraseSearchArticles("vacation policy");

// Search everywhere
const allResults = await exactPhraseSearchAll("vacation policy", "mock_hr", {
  limit: 15,
  case_sensitive: false,
});
```

## Performance Considerations

## Real-World Example: "vacation policy"

Here's how different search combinations work in practice:

### Option 1: Exact Phrase Only

```json
{
  "query": "vacation policy",
  "search_methods": {
    "exact_phrase": { "enabled": true, "weight": 1.0 }
  }
}
```

**Returns:** Only documents containing the literal phrase "vacation policy"
**Use case:** When you need the specific policy document

### Option 2: Semantic Only

```json
{
  "query": "vacation policy",
  "search_methods": {
    "vector_similarity": { "enabled": true, "weight": 1.0 }
  }
}
```

**Returns:** Documents about "time off", "PTO", "leave policies", "holiday schedules"
**Use case:** Exploratory search for related concepts

### Option 3: Hybrid (Recommended)

```json
{
  "query": "vacation policy",
  "search_methods": {
    "vector_similarity": { "enabled": true, "weight": 0.6 },
    "exact_phrase": { "enabled": true, "weight": 0.4 }
  }
}
```

**Returns:**

- Documents with "vacation policy" (ranked high due to exact match)
- PLUS documents about "PTO policies", "time off procedures", "leave management"
  **Use case:** Comprehensive search that doesn't miss anything

### Result Ranking Example

For the hybrid search above, results might rank like this:

1. **"Employee Vacation Policy"** (score: 0.95) - exact phrase match + semantic relevance
2. **"PTO and Time Off Guidelines"** (score: 0.78) - semantic match only
3. **"Annual Leave Procedures"** (score: 0.72) - semantic match only
4. **"Holiday Schedule and Vacation Policy"** (score: 0.88) - exact phrase + additional context

## Performance Considerations

### Method Selection Guidelines

**Use Semantic Search (high weight) when:**

- Query uses natural language
- Looking for conceptual matches
- User intent is broad or exploratory

**Use Sparse/Keyword Search (high weight) when:**

- Query contains specific terms or jargon
- Exact terminology is important
- Looking for precise matches

**Use Exact Phrase Search when:**

- Looking for specific phrases or quotes
- Policy names, procedure titles
- Exact wording is critical

**Use Manual Weighted Fusion when:**

- You need precise control over method contributions
- Weights differ significantly (>10% difference)
- Want to track individual method scores

### Performance Tips

1. **Limit Results:** Use appropriate `limit` values (10-20 for UI)
2. **Filter Early:** Apply filters to reduce search space
3. **Choose Appropriate Fusion:** RRF for balanced, Manual for precise control
4. **Multi-Stage for Large Data:** Use multi-stage for collections >10k documents
5. **Single Vector Mode:** Use for simple queries when fusion isn't needed

## Error Handling

Common error scenarios and solutions:

### Missing Environment Variables

```json
{
  "success": false,
  "error": "Server configuration error",
  "debug": { "missingEnvVars": ["QDRANT_API_KEY"] }
}
```

### Invalid Query

```json
{
  "success": false,
  "error": "Query is required",
  "debug": { "receivedQuery": "" }
}
```

### Vector Dimension Mismatch

```json
{
  "success": false,
  "error": "Vector dimension error: expected dim: 1024, got 512"
}
```

### SPLADE Service Unavailable

The system gracefully degrades to dense-only search when SPLADE service is unavailable.

## Testing

Use the comprehensive test suite to validate all search functionality:

```bash
cd advancedSearchAPI
npx tsx test-manual-fusion.ts
```

This tests all search methods, fusion techniques, and validates the complete search pipeline.
