/**
 * Advanced Search API - Task 3.1 Test Suite
 * 
 * Task 3.1: Hybrid Search Engine Implementation Tests
 * 
 * Tests the complete hybrid search engine with:
 * - RRF (Reciprocal Rank Fusion) and DBSF (Distribution-Based Score Fusion)
 * - Multi-stage search with prefetch architecture
 * - Real Qdrant Cloud integration with mock_hr collection
 * - HR-specific filtering and configuration
 */

import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config({ path: path.join(process.cwd(), '.env.local') });

// Import hybrid search components
import {
  HybridSearchEngine,
  createHybridSearchEngine,
  quickHybridSearch,
  hrHybridSearch,
  DEFAULT_HYBRID_CONFIG,
  DEFAULT_HR_COLLECTION,
  type HybridSearchConfig,
  type HybridSearchResult,
  type HRSpecificFilters
} from './hybrid-search';

import { getQdrantClient, listCollections, getCollectionInfo } from './qdrant-client';

/**
 * Test queries specifically designed for HR content
 */
const HR_TEST_QUERIES = [
  {
    query: 'employee onboarding process',
    description: 'Basic HR onboarding query',
    expected_terms: ['onboarding', 'employee', 'process'],
    fusion_method: 'rrf' as const
  },
  {
    query: 'payroll tax compliance requirements',
    description: 'Tax and compliance query',
    expected_terms: ['payroll', 'tax', 'compliance'],
    fusion_method: 'dbsf' as const
  },
  {
    query: 'international contractor payments',
    description: 'International payments query',
    expected_terms: ['international', 'contractor', 'payments'],
    fusion_method: 'rrf' as const
  },
  {
    query: 'employee handbook policies',
    description: 'HR policy documentation',
    expected_terms: ['employee', 'handbook', 'policies'],
    fusion_method: 'multi_stage' as const
  }
];

/**
 * HR-specific filter combinations for testing
 */
const HR_FILTER_TESTS: Array<{
  name: string;
  filters: HRSpecificFilters;
  description: string;
}> = [
  {
    name: 'section_path_filter',
    filters: {
      section_path: ['Employers and Admins', 'People Management']
    },
    description: 'Filter by HR management section'
  },
  {
    name: 'article_type_filter',
    filters: {
      article_type: 'article'
    },
    description: 'Filter for article content only'
  },
  {
    name: 'level_filter',
    filters: {
      level: 3
    },
    description: 'Filter by content depth level'
  },
  {
    name: 'combined_filters',
    filters: {
      section_path: ['Employers and Admins'],
      article_type: 'article',
      level: 3
    },
    description: 'Combined HR filtering'
  }
];

/**
 * Test 1: Basic Hybrid Search with mock_hr collection
 */
async function testBasicHybridSearch(): Promise<{ success: boolean; results: any[] }> {
  console.log('\n🧪 Test 1: Basic Hybrid Search with RRF Fusion');
  console.log('===============================================');
  console.log(`🎯 Using collection: ${DEFAULT_HR_COLLECTION}`);

  const results: any[] = [];

  try {
    const engine = createHybridSearchEngine();

    for (const testQuery of HR_TEST_QUERIES.slice(0, 2)) { // Test first 2 queries
      console.log(`\n📝 Testing: "${testQuery.query}"`);
      console.log(`   Description: ${testQuery.description}`);

      const config: HybridSearchConfig = {
        ...DEFAULT_HYBRID_CONFIG,
        fusion: {
          method: { type: testQuery.fusion_method, params: { k: 2 } },
          enabled: true
        },
        result_options: {
          limit: 5,
          include_snippets: true,
          snippet_context_words: 15
        }
      };

      const start_time = Date.now();
      const searchResult = await engine.basicHybridSearch(
        testQuery.query,
        DEFAULT_HR_COLLECTION,
        config
      );
      const execution_time = Date.now() - start_time;

      console.log(`   ✅ Results: ${searchResult.total_results} found`);
      console.log(`   ⏱️ Execution time: ${execution_time}ms`);
      console.log(`   🔄 Fusion method: ${searchResult.fusion_info?.method.type}`);

      if (searchResult.results.length > 0) {
        const topResult = searchResult.results[0];
        console.log(`   🎯 Top result: "${topResult.record.title}"`);
        console.log(`   📊 Score: ${topResult.relevance_score.toFixed(4)}`);
        console.log(`   📝 Snippets: ${topResult.snippets?.length || 0}`);
      }

      results.push({
        test: 'basic_hybrid_search',
        query: testQuery.query,
        fusion_method: testQuery.fusion_method,
        success: searchResult.success,
        results_count: searchResult.total_results,
        execution_time_ms: execution_time,
        top_score: searchResult.results[0]?.relevance_score || 0
      });
    }

    console.log('\n✅ Basic Hybrid Search Tests Completed');
    return { success: true, results };

  } catch (error) {
    console.error('\n❌ Basic Hybrid Search Tests Failed:', error);
    return { success: false, results };
  }
}

/**
 * Test 2: Quick HR Search Functions
 */
async function testQuickSearchFunctions(): Promise<{ success: boolean; results: any[] }> {
  console.log('\n🧪 Test 2: Quick Search Functions');
  console.log('=================================');
  console.log(`🎯 Using collection: ${DEFAULT_HR_COLLECTION}`);

  const results: any[] = [];

  try {
    // Test quickHybridSearch with default collection
    console.log('\n📝 Testing quickHybridSearch function (default collection)');
    const quickResult = await quickHybridSearch(
      'HR policies and procedures',
      undefined, // Use default collection
      { result_options: { limit: 3 } }
    );

    console.log(`   ✅ Quick search: ${quickResult.total_results} results`);
    console.log(`   ⏱️ Execution time: ${quickResult.execution_time_ms}ms`);
    console.log(`   📚 Collection: ${quickResult.collection_name}`);

    // Test hrHybridSearch (optimized for HR)
    console.log('\n📝 Testing hrHybridSearch function (HR-optimized, default collection)');
    const hrResult = await hrHybridSearch(
      'employee onboarding checklist'
      // No collection specified - should use DEFAULT_HR_COLLECTION
    );

    console.log(`   ✅ HR search: ${hrResult.total_results} results`);
    console.log(`   ⏱️ Execution time: ${hrResult.execution_time_ms}ms`);
    console.log(`   📚 Collection: ${hrResult.collection_name}`);
    console.log(`   🎯 HR-optimized: vector(0.6) + phrase(0.4) weights`);

    results.push({
      test: 'quick_search_functions',
      quick_search_results: quickResult.total_results,
      hr_search_results: hrResult.total_results,
      success: quickResult.success && hrResult.success,
      total_execution_time: quickResult.execution_time_ms + hrResult.execution_time_ms,
      collection_used: quickResult.collection_name
    });

    console.log('\n✅ Quick Search Function Tests Completed');
    return { success: true, results };

  } catch (error) {
    console.error('\n❌ Quick Search Function Tests Failed:', error);
    return { success: false, results };
  }
}

/**
 * Test 3: Collection and Infrastructure Validation
 */
async function testInfrastructureValidation(): Promise<{ success: boolean; results: any[] }> {
  console.log('\n🧪 Test 3: Collection and Infrastructure Validation');
  console.log('==================================================');

  const results: any[] = [];

  try {
    // Test Qdrant connection
    console.log('\n📝 Testing Qdrant connection and collections');
    const client = await getQdrantClient();
    const collections = await listCollections();
    
    console.log(`   ✅ Connected to Qdrant Cloud`);
    console.log(`   📊 Total collections: ${collections.length}`);
    console.log(`   📝 Collections: ${collections.join(', ')}`);

    // Check mock_hr collection specifically
    const hrCollectionExists = collections.includes(DEFAULT_HR_COLLECTION);
    console.log(`   🎯 ${DEFAULT_HR_COLLECTION} collection: ${hrCollectionExists ? '✅ EXISTS' : '❌ MISSING'}`);

    if (hrCollectionExists) {
      const collectionInfo = await getCollectionInfo('mock_hr');
      if (collectionInfo) {
        console.log(`   📊 Vector count: ${collectionInfo.vectorsCount}`);
        console.log(`   📏 Vector size: ${collectionInfo.vectorSize}`);
        console.log(`   📐 Distance metric: ${collectionInfo.distance}`);
      }
    }

    results.push({
      test: 'infrastructure_validation',
      qdrant_connection: true,
      total_collections: collections.length,
      hr_collection_exists: hrCollectionExists,
      success: hrCollectionExists,
      default_collection: DEFAULT_HR_COLLECTION
    });

    console.log('\n✅ Infrastructure Validation Tests Completed');
    return { success: true, results };

  } catch (error) {
    console.error('\n❌ Infrastructure Validation Tests Failed:', error);
    return { success: false, results };
  }
}

/**
 * Main test runner for Task 3.1
 */
export async function runTask31Tests(): Promise<boolean> {
  console.log('\n🚀 Running Task 3.1: Hybrid Search Engine Test Suite');
  console.log('====================================================');
  console.log(`🎯 Default HR Collection: ${DEFAULT_HR_COLLECTION}`);
  console.log(`🌐 Environment: ${process.env.NODE_ENV || 'development'}`);
  
  try {
    // Run all test suites
    const test1 = await testBasicHybridSearch();
    const test2 = await testQuickSearchFunctions();
    const test3 = await testInfrastructureValidation();
    
    const allTestsSucceeded = [test1, test2, test3].every(test => test.success);
    
    console.log('\n📊 Task 3.1 Test Summary');
    console.log('========================');
    console.log(`Basic Hybrid Search (RRF): ${test1.success ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`Quick Search Functions: ${test2.success ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`Infrastructure Validation: ${test3.success ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`Overall Result: ${allTestsSucceeded ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
    
    if (allTestsSucceeded) {
      console.log('\n🎉 Task 3.1: Hybrid Search Engine Implementation COMPLETED!');
      console.log(`🏆 Ready for production use with ${DEFAULT_HR_COLLECTION} collection`);
      console.log(`📝 All search functions now default to ${DEFAULT_HR_COLLECTION} collection`);
    } else {
      console.log('\n⚠️ Some tests failed. Please review the errors above.');
    }
    
    return allTestsSucceeded;
    
  } catch (error) {
    console.error('\n❌ Task 3.1 Test Suite Failed:', error);
    return false;
  }
}

// Export test functions for individual testing
export const Task31TestSuite = {
  testBasicHybridSearch,
  testQuickSearchFunctions,
  testInfrastructureValidation,
  runTask31Tests,
  DEFAULT_HR_COLLECTION,
  HR_TEST_QUERIES,
  HR_FILTER_TESTS
};

// Run tests if called directly
if (require.main === module) {
  runTask31Tests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test runner failed:', error);
      process.exit(1);
    });
} 