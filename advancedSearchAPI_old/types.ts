/**
 * Advanced Search API - Type Definitions
 * 
 * Task 1.1: Qdrant Cloud Integration
 * Comprehensive type definitions for the advanced search system
 */

/**
 * Qdrant Configuration
 */
export interface QdrantConfig {
  url: string;
  apiKey: string;
  timeout?: number;
  retries?: number;
}

/**
 * Collection Information
 */
export interface CollectionInfo {
  name: string;
  vectors_count: number;
  indexed_vectors_count: number;
  points_count: number;
  segments_count: number;
  status: string;
  optimizer_status: string;
  config: {
    params: {
      vectors: {
        size: number;
        distance: string;
      };
    };
  };
}

/**
 * Qdrant Search Result
 */
export interface QdrantSearchResult {
  id: string | number;
  version: number;
  score: number;
  payload: Record<string, any>;
  vector?: number[];
}

/**
 * Qdrant Point for indexing
 */
export interface QdrantPoint {
  id: string | number;
  vector: number[];
  payload: Record<string, any>;
}

/**
 * Search Parameters
 */
export interface QdrantSearchParams {
  vector: number[];
  limit?: number;
  offset?: number;
  filter?: Record<string, any>;
  params?: {
    hnsw_ef?: number;
    exact?: boolean;
  };
  with_payload?: boolean;
  with_vector?: boolean;
  score_threshold?: number;
}

/**
 * HR Search Record (optimized for search indexing)
 */
export interface HRSearchRecord {
  id: string;
  title: string;
  content?: string;
  summary?: string;
  fullContent?: string;
  updateDate?: string;
  breadcrumb: string;
  section_path: string[];
  article_type: 'section' | 'article';
  level: number;
  nodeType: 'category' | 'record';
  country?: string;
  htmlUrl?: string;
  sectionId?: number;
  recordId?: number;
}

/**
 * Voyage AI Embedding Response
 */
export interface VoyageEmbeddingResponse {
  data: Array<{
    embedding: number[];
    index: number;
  }>;
  model: string;
  usage: {
    total_tokens: number;
  };
}

/**
 * Sync Status for data migration pipeline
 */
export interface SyncStatus {
  success: boolean;
  graphName: string;
  collectionName: string;
  totalProcessed: number;
  totalSynced: number;
  errors: string[];
  duration: number;
  message: string;
  startTime?: string;
  endTime?: string;
}

/**
 * Batch Operation Result
 */
export interface BatchOperationResult {
  synced: number;
  errors: string[];
}

/**
 * Pagination Metadata
 */
export interface PaginationMeta {
  page: number;
  limit: number;
  offset: number;
  has_more: boolean;
  total_categories: number;
  total_records: number;
  returned_nodes: number;
  returned_records: number;
}

/**
 * API Response with Pagination
 */
export interface PaginatedAPIResponse {
  success: boolean;
  data: {
    nodes: any[];
    records: any[];
  };
  meta: {
    source: string;
    graph: string;
    timestamp: string;
    pagination: PaginationMeta;
    node_count: number;
    record_count: number;
  };
  error?: string;
}

/**
 * Error Classes
 */
export class QdrantConnectionError extends Error {
  constructor(message: string, public originalError?: any) {
    super(message);
    this.name = 'QdrantConnectionError';
  }
}

export class QdrantCollectionError extends Error {
  constructor(message: string, public collectionName?: string, public originalError?: any) {
    super(message);
    this.name = 'QdrantCollectionError';
  }
}

export class VoyageAIError extends Error {
  constructor(message: string, public originalError?: any) {
    super(message);
    this.name = 'VoyageAIError';
  }
}

export class SyncError extends Error {
  constructor(message: string, public syncStatus?: Partial<SyncStatus>, public originalError?: any) {
    super(message);
    this.name = 'SyncError';
  }
}

/**
 * Configuration Types
 */
export interface AdvancedSearchConfig {
  QDRANT_URL: string;
  QDRANT_API_KEY: string;
  VOYAGE_API_KEY: string;
  NODE_ENV?: string;
}

/**
 * Collection Configuration
 */
export interface CollectionConfig {
  vectors: {
    size: number;
    distance: 'Cosine' | 'Euclid' | 'Dot';
  };
  optimizers_config?: {
    deleted_threshold?: number;
    vacuum_min_vector_number?: number;
    default_segment_number?: number;
  };
  replication_factor?: number;
  write_consistency_factor?: number;
}

/**
 * Search Method Types
 */
export type SearchMethod = 'splade' | 'exact_words' | 'exact_phrase' | 'vector_similarity' | 'hybrid';

/**
 * Single Vector Search Configuration
 * For searching only specific named vectors (sparse or dense)
 */
export interface SingleVectorMode {
  enabled: boolean;
  vector_type: 'sparse' | 'dense';
}

/**
 * Fusion Method Configuration
 */
export interface FusionMethodConfig {
  method: { 
    type: 'rrf' | 'dbsf' | 'multi_stage' | 'manual_weighted' | 'exact_phrase';
    params?: {
      k?: number; // RRF constant
      alpha?: number; // DBSF alpha parameter
      vectorWeight?: number; // Manual weighted fusion semantic weight
      sparseWeight?: number; // Manual weighted fusion keyword weight
    };
  };
  enabled: boolean;
}

/**
 * Multi-stage Search Configuration
 */
export interface MultiStageConfig {
  enabled: boolean;
  stages: {
    prefetch_limit: number;
    using?: string;
    query_type: 'dense' | 'sparse' | 'byte';
  }[];
}

/**
 * Advanced Search Request
 */
export interface AdvancedSearchRequest {
  query: string;
  graph_name: string;
  search_methods: {
    splade?: { enabled: boolean; weight: number };
    exact_words?: { enabled: boolean; weight: number };
    exact_phrase?: { 
      enabled: boolean; 
      weight: number;
      options?: {
        search_fields?: ContentFieldType[];
        entity_types?: EntityType[];
        case_sensitive?: boolean;
        whole_words_only?: boolean;
      };
    };
    vector_similarity?: {
      enabled: boolean;
      weight: number;
      model?: 'voyage-3.5';
      dimension?: 256 | 512 | 1024 | 2048;
    };
    hybrid?: {
      enabled: boolean;
      vector_weight: number;
      phrase_weight: number;
    };
    sparse_vectors?: {
      enabled: boolean;
      weight: number;
    };
  };
  // Single vector mode for searching only specific named vectors
  single_vector_mode?: SingleVectorMode;
  // Fusion configuration for hybrid search
  fusion?: FusionMethodConfig;
  // Multi-stage search configuration
  multi_stage?: MultiStageConfig;
  // HR-specific filters
  filters?: {
    section_path?: string[];
    article_type?: 'section' | 'article';
    level?: number;
    update_date_after?: string;
  };
  // Result formatting options
  result_options?: {
    limit?: number;
    offset?: number;
    include_snippets?: boolean;
    snippet_context_words?: number;
  };
}

/**
 * Advanced Search Response
 */
export interface AdvancedSearchResponse {
  success: boolean;
  query: string;
  graph_name: string;
  collection_name: string;
  search_mode?: 'single_vector' | 'fusion';
  vector_type?: 'sparse' | 'dense';
  total_results: number;
  execution_time_ms: number;
  results: SearchResult[];
  method_scores?: {
    [method: string]: {
      results_count: number;
      avg_score: number;
    };
  };
  fusion_info?: {
    method: { type: string };
    combined_results: number;
    unique_results: number;
  };
}

/**
 * Individual Search Result
 */
export interface SearchResult {
  record: HRSearchRecord;
  relevance_score: number;
  method_contributions: {
    [method: string]: number;
  };
  snippets?: SearchSnippet[];
}

/**
 * Search Snippet
 */
export interface SearchSnippet {
  text: string;
  context_before: string;
  context_after: string;
  start_position: number;
  end_position: number;
  relevance_score: number;
}

/**
 * Content field types for exact phrase matching
 */
export type ContentFieldType = 'title' | 'content' | 'summary' | 'fullContent';

/**
 * Entity types in the system
 */
export type EntityType = 'node' | 'article' | 'both';

/**
 * Exact phrase search options
 */
export interface ExactPhraseSearchOptions {
  limit?: number;
  search_fields?: ContentFieldType[];
  entity_types?: EntityType[]; // Filter by node/article types
  include_snippets?: boolean;
  snippet_context_words?: number;
  case_sensitive?: boolean;
  whole_words_only?: boolean;
}

/**
 * Field mapping for different entity types
 */
export interface FieldMapping {
  nodes: {
    title: 'title';
    content: 'content';
  };
  articles: {
    title: 'title';
    summary: 'summary';
    fullContent: 'fullContent';
  };
} 