/**
 * Advanced Search API - Weight Experiment Test Suite
 * 
 * Testing hybrid search with different weight configurations for:
 * - Dense vectors (semantic similarity via Voyage AI)
 * - Sparse vectors (keyword matching via SPLADE)
 * 
 * This will help determine optimal weight combinations for different query types.
 */

import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config({ path: path.join(process.cwd(), '.env.local') });

import {
  HybridSearchEngine,
  createHybridSearchEngine,
  DEFAULT_HR_COLLECTION,
  type HybridSearchConfig,
  type HybridSearchResult
} from './hybrid-search';

/**
 * Weight configuration test cases
 */
const WEIGHT_EXPERIMENTS = [
  {
    name: 'semantic_heavy',
    description: 'Heavy semantic focus (dense: 0.8, sparse: 0.2)',
    weights: { dense: 0.8, sparse: 0.2 }
  },
  {
    name: 'balanced',
    description: 'Balanced approach (dense: 0.5, sparse: 0.5)',
    weights: { dense: 0.5, sparse: 0.5 }
  },
  {
    name: 'keyword_heavy',
    description: 'Heavy keyword focus (dense: 0.2, sparse: 0.8)',
    weights: { dense: 0.2, sparse: 0.8 }
  },
  {
    name: 'slight_semantic',
    description: 'Slight semantic preference (dense: 0.6, sparse: 0.4)',
    weights: { dense: 0.6, sparse: 0.4 }
  },
  {
    name: 'slight_keyword',
    description: 'Slight keyword preference (dense: 0.4, sparse: 0.6)',
    weights: { dense: 0.4, sparse: 0.6 }
  }
];

/**
 * Diverse test queries for different search scenarios
 */
const TEST_QUERIES = [
  {
    query: 'employee onboarding process',
    type: 'procedural',
    description: 'Process-oriented query'
  },
  {
    query: 'payroll tax compliance',
    type: 'compliance',
    description: 'Regulatory/compliance query'
  },
  {
    query: 'Form I-9',
    type: 'specific_document',
    description: 'Exact document/form reference'
  },
  {
    query: 'international contractor payments',
    type: 'conceptual',
    description: 'Multi-concept query'
  },
  {
    query: 'offer letter components',
    type: 'structural',
    description: 'Document structure query'
  }
];

/**
 * Run weight experiment for a single query
 */
async function runWeightExperiment(
  engine: HybridSearchEngine,
  query: string,
  queryType: string,
  weights: { dense: number; sparse: number }
): Promise<{
  query: string;
  queryType: string;
  weights: { dense: number; sparse: number };
  results: HybridSearchResult;
  topResult: any;
  avgScore: number;
  executionTime: number;
}> {
  const config: HybridSearchConfig = {
    search_methods: {
      vector_similarity: {
        enabled: true,
        weight: weights.dense,
        model: 'voyage-3.5',
        dimension: 1024,
        using: 'dense'
      },
      sparse_vectors: {
        enabled: true,
        weight: weights.sparse,
        using: 'sparse'
      },
      exact_phrase: {
        enabled: false,
        weight: 0.0
      }
    },
    fusion: {
      method: { type: 'rrf', params: { k: 2 } },
      enabled: true
    },
    result_options: {
      limit: 5,
      include_snippets: true,
      snippet_context_words: 20
    }
  };

  const startTime = Date.now();
  const searchResult = await engine.basicHybridSearch(query, DEFAULT_HR_COLLECTION, config);
  const executionTime = Date.now() - startTime;

  const avgScore = searchResult.results.length > 0 
    ? searchResult.results.reduce((sum, r) => sum + r.relevance_score, 0) / searchResult.results.length
    : 0;

  return {
    query,
    queryType,
    weights,
    results: searchResult,
    topResult: searchResult.results[0] || null,
    avgScore,
    executionTime
  };
}

/**
 * Compare weight configurations for all queries
 */
async function compareWeightConfigurations(): Promise<void> {
  console.log('\n🧪 Hybrid Search Weight Configuration Experiments');
  console.log('=================================================');
  console.log(`🎯 Collection: ${DEFAULT_HR_COLLECTION}`);
  console.log(`📊 Testing ${WEIGHT_EXPERIMENTS.length} weight configs × ${TEST_QUERIES.length} queries`);

  const engine = createHybridSearchEngine();
  const results: any[] = [];

  // Test each query with each weight configuration
  for (const testQuery of TEST_QUERIES) {
    console.log(`\n📝 Query: "${testQuery.query}" (${testQuery.type})`);
    console.log(`    ${testQuery.description}`);
    console.log('    ' + '='.repeat(50));

    const queryResults: any[] = [];

    for (const weightConfig of WEIGHT_EXPERIMENTS) {
      console.log(`\n   🎛️  ${weightConfig.name}: ${weightConfig.description}`);
      
      try {
        const experimentResult = await runWeightExperiment(
          engine,
          testQuery.query,
          testQuery.type,
          weightConfig.weights
        );

        console.log(`      📊 Results: ${experimentResult.results.total_results}`);
        console.log(`      ⏱️  Time: ${experimentResult.executionTime}ms`);
        console.log(`      📈 Avg Score: ${experimentResult.avgScore.toFixed(4)}`);
        
        if (experimentResult.topResult) {
          console.log(`      🎯 Top: "${experimentResult.topResult.record.title}"`);
          console.log(`      🏆 Score: ${experimentResult.topResult.relevance_score.toFixed(4)}`);
        }

        queryResults.push({
          weightConfig: weightConfig.name,
          weights: weightConfig.weights,
          resultsCount: experimentResult.results.total_results,
          avgScore: experimentResult.avgScore,
          topScore: experimentResult.topResult?.relevance_score || 0,
          topTitle: experimentResult.topResult?.record.title || 'No results',
          executionTime: experimentResult.executionTime
        });

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.log(`      ❌ Error: ${errorMessage}`);
        queryResults.push({
          weightConfig: weightConfig.name,
          weights: weightConfig.weights,
          error: errorMessage
        });
      }
    }

    // Find best configuration for this query
    const bestConfig = queryResults
      .filter(r => !r.error)
      .sort((a, b) => b.topScore - a.topScore)[0];

    if (bestConfig) {
      console.log(`\n   🏆 Best config for "${testQuery.query}": ${bestConfig.weightConfig}`);
      console.log(`      Dense: ${bestConfig.weights.dense}, Sparse: ${bestConfig.weights.sparse}`);
      console.log(`      Score: ${bestConfig.topScore.toFixed(4)}, Results: ${bestConfig.resultsCount}`);
    }

    results.push({
      query: testQuery.query,
      queryType: testQuery.type,
      description: testQuery.description,
      configs: queryResults,
      bestConfig: bestConfig
    });
  }

  // Overall analysis
  console.log('\n📊 Weight Configuration Analysis');
  console.log('================================');

  // Count wins by configuration
  const configWins: Record<string, number> = {};
  const configAvgScores: Record<string, number[]> = {};

  WEIGHT_EXPERIMENTS.forEach(config => {
    configWins[config.name] = 0;
    configAvgScores[config.name] = [];
  });

  results.forEach(queryResult => {
    if (queryResult.bestConfig) {
      configWins[queryResult.bestConfig.weightConfig]++;
    }
    
    queryResult.configs.forEach((config: any) => {
      if (!config.error) {
        configAvgScores[config.weightConfig].push(config.topScore);
      }
    });
  });

  // Calculate average scores across all queries
  const avgPerformance: Array<{ name: string; avgScore: number; wins: number }> = 
    WEIGHT_EXPERIMENTS.map(config => ({
      name: config.name,
      avgScore: configAvgScores[config.name].length > 0 
        ? configAvgScores[config.name].reduce((sum, score) => sum + score, 0) / configAvgScores[config.name].length
        : 0,
      wins: configWins[config.name]
    }))
    .sort((a, b) => b.avgScore - a.avgScore);

  console.log('\n🏆 Configuration Rankings (by average score):');
  avgPerformance.forEach((config, index) => {
    const rank = index + 1;
    const medal = rank === 1 ? '🥇' : rank === 2 ? '🥈' : rank === 3 ? '🥉' : '  ';
    console.log(`${medal} ${rank}. ${config.name}: ${config.avgScore.toFixed(4)} avg score, ${config.wins} wins`);
  });

  console.log('\n💡 Recommendations:');
  const topConfig = avgPerformance[0];
  const topWeights = WEIGHT_EXPERIMENTS.find(w => w.name === topConfig.name)?.weights;
  
  if (topWeights) {
    console.log(`   🎯 Best overall: ${topConfig.name}`);
    console.log(`   ⚖️  Optimal weights: Dense ${topWeights.dense}, Sparse ${topWeights.sparse}`);
    console.log(`   📈 Performance: ${topConfig.avgScore.toFixed(4)} average score`);
    console.log(`   🏆 Query wins: ${topConfig.wins}/${TEST_QUERIES.length}`);
  }

  // Query type analysis
  console.log('\n📝 Query Type Insights:');
  const queryTypeInsights = TEST_QUERIES.map(q => {
    const result = results.find(r => r.query === q.query);
    return {
      type: q.type,
      query: q.query,
      bestConfig: result?.bestConfig?.weightConfig || 'none',
      bestWeights: result?.bestConfig?.weights || null
    };
  });

  const typeGroups = queryTypeInsights.reduce((groups, insight) => {
    if (!groups[insight.type]) groups[insight.type] = [];
    groups[insight.type].push(insight);
    return groups;
  }, {} as Record<string, any[]>);

  Object.entries(typeGroups).forEach(([type, insights]) => {
    console.log(`   📋 ${type}:`);
    insights.forEach(insight => {
      if (insight.bestWeights) {
        console.log(`      "${insight.query}" → ${insight.bestConfig} (${insight.bestWeights.dense}/${insight.bestWeights.sparse})`);
      }
    });
  });
}

/**
 * Main function
 */
async function main(): Promise<void> {
  try {
    await compareWeightConfigurations();
    console.log('\n✅ Weight experiments completed successfully!');
  } catch (error) {
    console.error('\n❌ Weight experiments failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('Weight experiment runner failed:', error);
    process.exit(1);
  });
}

export { compareWeightConfigurations, WEIGHT_EXPERIMENTS, TEST_QUERIES }; 