/**
 * Test Script: Manual Fusion and Search Types Validation
 * 
 * This script tests all search functionality including:
 * - Basic hybrid search with RRF fusion
 * - Manual weighted fusion (the main fix)
 * - Multi-stage search
 * - Exact phrase search
 * - Individual search methods
 */

import { HybridSearchEngine, DEFAULT_HYBRID_CONFIG, type HybridSearchConfig } from './hybrid-search';
import { syncFalkorToQdrant, getSyncStatus } from './sync-service';
import { getQdrantClient } from './qdrant-client';

// Test configuration
const TEST_COLLECTION = 'mock_hr';
const TEST_QUERIES = [
  'employee benefits package',
  'vacation policy',
  'remote work guidelines',
  'performance review process',
  'company holidays'
];

interface TestResult {
  searchType: string;
  query: string;
  success: boolean;
  resultCount: number;
  executionTime: number;
  topScore: number;
  hasSemanticScores: boolean;
  hasKeywordScores: boolean;
  fusionMethod?: string;
  error?: string;
}

/**
 * Main test runner
 */
async function runAllTests(): Promise<void> {
  console.log('🧪 Starting Manual Fusion and Search Tests');
  console.log('='.repeat(50));

  try {
    // 1. Check if data exists, sync if needed
    await ensureTestData();

    // 2. Initialize search engine
    const searchEngine = new HybridSearchEngine();

    // 3. Run all test types
    const results: TestResult[] = [];

    for (const query of TEST_QUERIES) {
      console.log(`\n🔍 Testing query: "${query}"`);
      console.log('-'.repeat(40));

      // Test 1: Basic Hybrid Search (RRF/balanced weights)
      const basicResult = await testBasicHybridSearch(searchEngine, query);
      results.push(basicResult);

      // Test 2: Manual Weighted Fusion (unbalanced weights)
      const manualResult = await testManualWeightedFusion(searchEngine, query);
      results.push(manualResult);

      // Test 3: Multi-stage Search
      const multiStageResult = await testMultiStageSearch(searchEngine, query);
      results.push(multiStageResult);

      // Test 4: Exact Phrase Search
      const exactPhraseResult = await testExactPhraseSearch(searchEngine, query);
      results.push(exactPhraseResult);

      // Test 5: Advanced Search (DBSF)
      const advancedResult = await testAdvancedSearch(searchEngine, query);
      results.push(advancedResult);
    }

    // 4. Analyze and report results
    await analyzeResults(results);

  } catch (error) {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
  }
}

/**
 * Ensure test data exists in Qdrant
 */
async function ensureTestData(): Promise<void> {
  console.log('📊 Checking test data availability...');

  try {
    const status = await getSyncStatus(TEST_COLLECTION);
    
    if (!status.collectionExists || status.pointCount === 0) {
      console.log('📥 No data found, syncing from FalkorDB...');
      
      const syncResult = await syncFalkorToQdrant(TEST_COLLECTION, {
        batchSize: 100,
        forceRecreate: true
      });

      if (!syncResult.success) {
        throw new Error(`Sync failed: ${syncResult.errors.join(', ')}`);
      }

      console.log(`✅ Synced ${syncResult.totalSynced} records to collection`);
    } else {
      console.log(`✅ Found existing collection with ${status.pointCount} points`);
    }
  } catch (error) {
    console.error('❌ Data setup failed:', error);
    throw error;
  }
}

/**
 * Test 1: Basic Hybrid Search (balanced weights, should use RRF)
 */
async function testBasicHybridSearch(engine: HybridSearchEngine, query: string): Promise<TestResult> {
  console.log('  🔄 Testing Basic Hybrid Search (RRF)...');
  
  const config: HybridSearchConfig = {
    ...DEFAULT_HYBRID_CONFIG,
    search_methods: {
      vector_similarity: { enabled: true, weight: 0.5 }, // Balanced
      sparse_vectors: { enabled: true, weight: 0.5 }     // Balanced
    },
    fusion: { method: { type: 'rrf' }, enabled: true }
  };

  try {
    const startTime = Date.now();
    const result = await engine.basicHybridSearch(query, TEST_COLLECTION, config);
    const executionTime = Date.now() - startTime;

    return {
      searchType: 'Basic Hybrid (RRF)',
      query,
      success: result.success,
      resultCount: result.results.length,
      executionTime,
      topScore: result.results[0]?.relevance_score || 0,
      hasSemanticScores: false, // RRF doesn't expose individual method scores
      hasKeywordScores: false,
      fusionMethod: result.fusion_info?.method.type
    };
  } catch (error) {
    return {
      searchType: 'Basic Hybrid (RRF)',
      query,
      success: false,
      resultCount: 0,
      executionTime: 0,
      topScore: 0,
      hasSemanticScores: false,
      hasKeywordScores: false,
      error: String(error)
    };
  }
}

/**
 * Test 2: Manual Weighted Fusion (unbalanced weights, should trigger manual fusion)
 */
async function testManualWeightedFusion(engine: HybridSearchEngine, query: string): Promise<TestResult> {
  console.log('  🎛️  Testing Manual Weighted Fusion (90% semantic, 10% keyword)...');
  
  const config: HybridSearchConfig = {
    ...DEFAULT_HYBRID_CONFIG,
    search_methods: {
      vector_similarity: { enabled: true, weight: 0.9 }, // High weight
      sparse_vectors: { enabled: true, weight: 0.1 }     // Low weight
    },
    fusion: { method: { type: 'manual_weighted' }, enabled: true }
  };

  try {
    const startTime = Date.now();
    const result = await engine.basicHybridSearch(query, TEST_COLLECTION, config);
    const executionTime = Date.now() - startTime;

    // Check if manual fusion was actually used
    const isManualFusion = result.fusion_info?.method.type === 'manual_weighted';
    const hasIndividualScores = result.results[0]?.method_contributions?.semantic !== undefined &&
                               result.results[0]?.method_contributions?.keyword !== undefined;

    console.log(`    ✅ Manual fusion used: ${isManualFusion}`);
    console.log(`    ✅ Individual scores available: ${hasIndividualScores}`);
    if (result.results[0]) {
      console.log(`    📊 Top result contributions:`, result.results[0].method_contributions);
    }

    return {
      searchType: 'Manual Weighted Fusion',
      query,
      success: result.success && isManualFusion,
      resultCount: result.results.length,
      executionTime,
      topScore: result.results[0]?.relevance_score || 0,
      hasSemanticScores: hasIndividualScores,
      hasKeywordScores: hasIndividualScores,
      fusionMethod: result.fusion_info?.method.type
    };
  } catch (error) {
    return {
      searchType: 'Manual Weighted Fusion',
      query,
      success: false,
      resultCount: 0,
      executionTime: 0,
      topScore: 0,
      hasSemanticScores: false,
      hasKeywordScores: false,
      error: String(error)
    };
  }
}

/**
 * Test 3: Multi-stage Search
 */
async function testMultiStageSearch(engine: HybridSearchEngine, query: string): Promise<TestResult> {
  console.log('  🔄 Testing Multi-stage Search...');
  
  const config: HybridSearchConfig = {
    ...DEFAULT_HYBRID_CONFIG,
    multi_stage: {
      enabled: true,
      stages: [
        { prefetch_limit: 1000, using: 'dense', query_type: 'dense' },
        { prefetch_limit: 100, using: 'dense', query_type: 'dense' }
      ]
    }
  };

  try {
    const startTime = Date.now();
    const result = await engine.multiStageHybridSearch(query, TEST_COLLECTION, config);
    const executionTime = Date.now() - startTime;

    return {
      searchType: 'Multi-stage Search',
      query,
      success: result.success,
      resultCount: result.results.length,
      executionTime,
      topScore: result.results[0]?.relevance_score || 0,
      hasSemanticScores: false,
      hasKeywordScores: false,
      fusionMethod: result.fusion_info?.method.type
    };
  } catch (error) {
    return {
      searchType: 'Multi-stage Search',
      query,
      success: false,
      resultCount: 0,
      executionTime: 0,
      topScore: 0,
      hasSemanticScores: false,
      hasKeywordScores: false,
      error: String(error)
    };
  }
}

/**
 * Test 4: Exact Phrase Search
 */
async function testExactPhraseSearch(engine: HybridSearchEngine, query: string): Promise<TestResult> {
  console.log('  🎯 Testing Exact Phrase Search...');
  
  try {
    const startTime = Date.now();
    const result = await engine.exactPhraseMatch(query, TEST_COLLECTION, {
      limit: 10,
      search_fields: ['title', 'content', 'summary'],
      include_snippets: true
    });
    const executionTime = Date.now() - startTime;

    return {
      searchType: 'Exact Phrase Search',
      query,
      success: result.success,
      resultCount: result.results.length,
      executionTime,
      topScore: result.results[0]?.relevance_score || 0,
      hasSemanticScores: false,
      hasKeywordScores: false,
      fusionMethod: result.fusion_info?.method.type
    };
  } catch (error) {
    return {
      searchType: 'Exact Phrase Search',
      query,
      success: false,
      resultCount: 0,
      executionTime: 0,
      topScore: 0,
      hasSemanticScores: false,
      hasKeywordScores: false,
      error: String(error)
    };
  }
}

/**
 * Test 5: Advanced Search (DBSF)
 */
async function testAdvancedSearch(engine: HybridSearchEngine, query: string): Promise<TestResult> {
  console.log('  🧠 Testing Advanced Search (DBSF)...');
  
  const config: HybridSearchConfig = {
    ...DEFAULT_HYBRID_CONFIG,
    fusion: { method: { type: 'dbsf' }, enabled: true }
  };

  try {
    const startTime = Date.now();
    const result = await engine.advancedHybridSearch(query, TEST_COLLECTION, config);
    const executionTime = Date.now() - startTime;

    return {
      searchType: 'Advanced Search (DBSF)',
      query,
      success: result.success,
      resultCount: result.results.length,
      executionTime,
      topScore: result.results[0]?.relevance_score || 0,
      hasSemanticScores: false,
      hasKeywordScores: false,
      fusionMethod: result.fusion_info?.method.type
    };
  } catch (error) {
    return {
      searchType: 'Advanced Search (DBSF)',
      query,
      success: false,
      resultCount: 0,
      executionTime: 0,
      topScore: 0,
      hasSemanticScores: false,
      hasKeywordScores: false,
      error: String(error)
    };
  }
}

/**
 * Analyze and report test results
 */
async function analyzeResults(results: TestResult[]): Promise<void> {
  console.log('\n📊 TEST RESULTS ANALYSIS');
  console.log('='.repeat(50));

  // Group results by search type
  const bySearchType = results.reduce((acc, result) => {
    if (!acc[result.searchType]) {
      acc[result.searchType] = [];
    }
    acc[result.searchType].push(result);
    return acc;
  }, {} as Record<string, TestResult[]>);

  // Analyze each search type
  for (const [searchType, typeResults] of Object.entries(bySearchType)) {
    console.log(`\n🔍 ${searchType}`);
    console.log('-'.repeat(30));

    const successful = typeResults.filter(r => r.success);
    const failed = typeResults.filter(r => !r.success);

    console.log(`  ✅ Successful: ${successful.length}/${typeResults.length}`);
    console.log(`  ❌ Failed: ${failed.length}/${typeResults.length}`);

    if (successful.length > 0) {
      const avgExecutionTime = successful.reduce((sum, r) => sum + r.executionTime, 0) / successful.length;
      const avgResultCount = successful.reduce((sum, r) => sum + r.resultCount, 0) / successful.length;
      const avgTopScore = successful.reduce((sum, r) => sum + r.topScore, 0) / successful.length;

      console.log(`  ⏱️  Avg execution time: ${avgExecutionTime.toFixed(0)}ms`);
      console.log(`  📊 Avg result count: ${avgResultCount.toFixed(1)}`);
      console.log(`  🎯 Avg top score: ${avgTopScore.toFixed(3)}`);

      // Special analysis for manual fusion
      if (searchType === 'Manual Weighted Fusion') {
        const withIndividualScores = successful.filter(r => r.hasSemanticScores && r.hasKeywordScores);
        console.log(`  🎛️  Individual scores available: ${withIndividualScores.length}/${successful.length}`);
        
        if (withIndividualScores.length === successful.length) {
          console.log(`  ✅ MANUAL FUSION IS WORKING CORRECTLY!`);
        } else {
          console.log(`  ⚠️  Manual fusion may have issues - not all results have individual scores`);
        }
      }
    }

    if (failed.length > 0) {
      console.log(`  ❌ Error examples:`);
      failed.slice(0, 2).forEach(result => {
        console.log(`    - Query: "${result.query}" | Error: ${result.error}`);
      });
    }
  }

  // Overall summary
  console.log('\n🏆 OVERALL SUMMARY');
  console.log('-'.repeat(20));
  const totalSuccessful = results.filter(r => r.success).length;
  const totalTests = results.length;
  const successRate = (totalSuccessful / totalTests * 100).toFixed(1);

  console.log(`📊 Success rate: ${totalSuccessful}/${totalTests} (${successRate}%)`);

  // Check critical functionality
  const manualFusionResults = results.filter(r => r.searchType === 'Manual Weighted Fusion');
  const manualFusionWorking = manualFusionResults.every(r => r.success && r.hasSemanticScores);

  if (manualFusionWorking) {
    console.log('✅ CRITICAL: Manual weighted fusion is working correctly');
  } else {
    console.log('❌ CRITICAL: Manual weighted fusion has issues');
  }

  // Test individual search method isolation
  await testIndividualMethods();
}

/**
 * Test individual search methods to ensure they work in isolation
 */
async function testIndividualMethods(): Promise<void> {
  console.log('\n🧪 TESTING INDIVIDUAL SEARCH METHODS');
  console.log('-'.repeat(40));

  const client = await getQdrantClient();
  const testQuery = TEST_QUERIES[0]; // Use first query for simplicity

  try {
    // Test 1: Dense vector search only
    console.log('  🔍 Testing dense vector search...');
    const denseResponse = await client.query(TEST_COLLECTION, {
      query: [0.1, 0.2, 0.3], // Dummy vector for testing
      using: 'dense',
      limit: 5,
      with_payload: true
    });
    console.log(`    ✅ Dense search: ${denseResponse.points?.length || 0} results`);

    // Test 2: Sparse vector search only  
    console.log('  🕸️  Testing sparse vector search...');
    const sparseResponse = await client.query(TEST_COLLECTION, {
      query: { indices: [1, 5, 10], values: [0.1, 0.5, 0.3] },
      using: 'sparse',
      limit: 5,
      with_payload: true
    });
    console.log(`    ✅ Sparse search: ${sparseResponse.points?.length || 0} results`);

    // Test 3: Collection info
    console.log('  📊 Testing collection info...');
    const collectionInfo = await client.getCollection(TEST_COLLECTION);
    console.log(`    ✅ Collection points: ${collectionInfo.points_count}`);
    console.log(`    ✅ Vector configs:`, Object.keys(collectionInfo.config?.params?.vectors || {}));

  } catch (error) {
    console.error('❌ Individual method test failed:', error);
  }
}

/**
 * Run the test suite
 */
if (require.main === module) {
  runAllTests()
    .then(() => {
      console.log('\n🎉 Test suite completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Test suite failed:', error);
      process.exit(1);
    });
}

export { runAllTests, testManualWeightedFusion }; 