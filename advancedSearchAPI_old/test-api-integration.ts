/**
 * Test API Integration - Verify API Route uses HybridSearchEngine
 * 
 * This test validates that the API route properly connects to and uses
 * the HybridSearchEngine for all search operations.
 */

import { NextRequest } from 'next/server';

async function testAPIIntegration() {
  console.log('🧪 Testing API Integration with HybridSearchEngine');
  console.log('=' .repeat(60));

  const testCases = [
    {
      name: 'Basic Hybrid Search',
      request: {
        query: 'employee benefits',
        graph_name: 'mock_hr',
        search_methods: {
          vector_similarity: { enabled: true, weight: 0.7 },
          exact_phrase: { enabled: true, weight: 0.3 }
        },
        result_options: {
          limit: 5,
          include_snippets: true
        }
      }
    },
    {
      name: 'Single Vector Mode - Dense',
      request: {
        query: 'performance review',
        graph_name: 'mock_hr',
        search_methods: {
          vector_similarity: { enabled: true, weight: 1.0 }
        },
        single_vector_mode: {
          enabled: true,
          vector_type: 'dense' as const
        },
        result_options: {
          limit: 5
        }
      }
    },
    {
      name: 'Exact Phrase Search',
      request: {
        query: 'remote work policy',
        graph_name: 'mock_hr',
        search_methods: {
          exact_phrase: { 
            enabled: true, 
            weight: 1.0,
            options: {
              search_fields: ['title', 'content'],
              entity_types: ['both'],
              case_sensitive: false
            }
          }
        },
        result_options: {
          limit: 5,
          include_snippets: true
        }
      }
    },
    {
      name: 'Manual Weighted Fusion',
      request: {
        query: 'training program',
        graph_name: 'mock_hr',
        search_methods: {
          vector_similarity: { enabled: true, weight: 0.9 },
          sparse_vectors: { enabled: true, weight: 0.1 }
        },
        result_options: {
          limit: 5
        }
      }
    },
    {
      name: 'DBSF Fusion',
      request: {
        query: 'company culture',
        graph_name: 'mock_hr',
        search_methods: {
          vector_similarity: { enabled: true, weight: 0.6 },
          sparse_vectors: { enabled: true, weight: 0.4 }
        },
        fusion: {
          method: { type: 'dbsf' as const },
          enabled: true
        },
        result_options: {
          limit: 5
        }
      }
    }
  ];

  let passedTests = 0;
  let totalTests = testCases.length;

  for (const testCase of testCases) {
    console.log(`\n🔍 Testing: ${testCase.name}`);
    console.log('-'.repeat(40));

    try {
      // Create a mock NextRequest
      const mockRequest = {
        json: async () => testCase.request
      } as NextRequest;

      // Import and call the API route
      const { POST } = await import('../app/api/search/advanced/route');
      const response = await POST(mockRequest);
      
      // Parse the response
      const result = await response.json();
      
      // Validate response structure
      if (result.success) {
        console.log(`✅ API call successful`);
        console.log(`📊 Results: ${result.total_results} found`);
        console.log(`⏱️ Execution time: ${result.execution_time_ms}ms`);
        console.log(`🔄 Search mode: ${result.search_mode || 'fusion'}`);
        
        if (result.fusion_info) {
          console.log(`🔀 Fusion method: ${result.fusion_info.method.type}`);
        }
        
        // Validate result structure
        if (result.results && Array.isArray(result.results)) {
          const firstResult = result.results[0];
          if (firstResult && firstResult.record && firstResult.relevance_score !== undefined) {
            console.log(`📝 Sample result: "${firstResult.record.title}"`);
            console.log(`📈 Score: ${firstResult.relevance_score.toFixed(3)}`);
            
            if (firstResult.method_contributions) {
              console.log(`🎯 Method contributions:`, Object.keys(firstResult.method_contributions));
            }
            
            passedTests++;
          } else {
            console.log(`❌ Invalid result structure`);
          }
        } else {
          console.log(`❌ No results array found`);
        }
      } else {
        console.log(`❌ API call failed: ${result.error}`);
        if (result.details) {
          console.log(`💬 Details: ${result.details}`);
        }
      }

    } catch (error) {
      console.log(`❌ Test failed with error:`, error);
    }
  }

  console.log('\n' + '='.repeat(60));
  console.log(`🏁 Integration Test Results: ${passedTests}/${totalTests} passed`);
  console.log(`📊 Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! API is properly integrated with HybridSearchEngine');
  } else {
    console.log('⚠️ Some tests failed. Check the integration.');
  }

  return {
    passed: passedTests,
    total: totalTests,
    success_rate: (passedTests / totalTests) * 100
  };
}

// Run the test if this file is executed directly
if (require.main === module) {
  testAPIIntegration().catch(console.error);
}

export { testAPIIntegration }; 