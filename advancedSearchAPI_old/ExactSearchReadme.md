# Exact Phrase Matching Implementation

## Overview

This implementation provides exact phrase matching capabilities for your Qdrant-based search system, supporting field-specific searches across different content types (nodes and articles) with full-text indexing.

## 🏗️ Data Structure

Your system contains two main entity types:

| Entity Type            | Fields                            | Description                                              |
| ---------------------- | --------------------------------- | -------------------------------------------------------- |
| **Nodes** (Categories) | `title`, `content`                | Section names and category descriptions                  |
| **Articles**           | `title`, `summary`, `fullContent` | Article titles, brief descriptions, and complete content |

## 🔧 Setup

### 1. Create Full-Text Indexes (One-time setup)

```typescript
import { createFullTextIndexes } from "./qdrant-client";

// Creates indexes for all content fields: title, content, summary, fullContent
await createFullTextIndexes("mock_hr");
```

This function automatically creates Qdrant full-text indexes on all required fields with optimized settings:

- **Tokenizer**: Word-based
- **Min token length**: 2 characters
- **Max token length**: 50 characters
- **Case handling**: Lowercase normalization

### 2. Verify Index Creation

```typescript
import { listPayloadIndexes } from "./qdrant-client";

const indexes = await listPayloadIndexes("mock_hr");
console.log("Available indexes:", Object.keys(indexes));
```

## 🚀 Usage

### Quick Start Functions

```typescript
import {
  exactPhraseSearchAll,
  exactPhraseSearchNodes,
  exactPhraseSearchArticles,
} from "./hybrid-search";

// Search across all fields and entity types
const allResults = await exactPhraseSearchAll("gross pay");

// Search only in nodes (categories) - title and content fields
const nodeResults = await exactPhraseSearchNodes("payroll taxes");

// Search only in articles - title, summary, and fullContent fields
const articleResults = await exactPhraseSearchArticles("employee benefits");
```

### Advanced Configuration

```typescript
import { HybridSearchEngine } from "./hybrid-search";

const engine = new HybridSearchEngine();

const results = await engine.exactPhraseMatch(
  "retirement contributions",
  "mock_hr",
  {
    search_fields: ["fullContent"], // Specific fields to search
    entity_types: ["article"], // Filter by entity type
    case_sensitive: false, // Case sensitivity
    whole_words_only: false, // Whole word matching
    include_snippets: true, // Include context snippets
    snippet_context_words: 20, // Words of context around match
    limit: 10, // Maximum results
  }
);
```

## ⚙️ Search Options

The exact phrase matching supports various configuration options to fine-tune search behavior:

| Option                      | Type       | Default                                          | Description                                          | Example                             |
| --------------------------- | ---------- | ------------------------------------------------ | ---------------------------------------------------- | ----------------------------------- |
| **`search_fields`**         | `string[]` | `["title", "content", "summary", "fullContent"]` | Specific content fields to search                    | `["title", "fullContent"]`          |
| **`entity_types`**          | `string[]` | `["node", "article"]`                            | Filter by entity type (nodes/categories or articles) | `["article"]`                       |
| **`case_sensitive`**        | `boolean`  | `false`                                          | Enable exact case matching                           | `true` for "Form I-9" vs "form i-9" |
| **`whole_words_only`**      | `boolean`  | `false`                                          | Match only complete words (word boundaries)          | `true` to avoid partial matches     |
| **`include_snippets`**      | `boolean`  | `true`                                           | Include text snippets around matches                 | `false` for faster queries          |
| **`snippet_context_words`** | `number`   | `15`                                             | Number of words before/after match in snippets       | `10` for shorter context            |
| **`limit`**                 | `number`   | `10`                                             | Maximum number of results to return                  | `5` for fewer results               |

### Search Field Options

| Field             | Available For    | Content Type          | Use Case                               |
| ----------------- | ---------------- | --------------------- | -------------------------------------- |
| **`title`**       | Nodes & Articles | Titles/headings       | Find specific section or article names |
| **`content`**     | Nodes only       | Category descriptions | Search category/section descriptions   |
| **`summary`**     | Articles only    | Brief descriptions    | Find article summaries                 |
| **`fullContent`** | Articles only    | Complete article text | Deep content search                    |

### Entity Type Options

| Entity Type               | Description         | Fields Available                  | Example Use                        |
| ------------------------- | ------------------- | --------------------------------- | ---------------------------------- |
| **`node`**                | Categories/sections | `title`, `content`                | Find administrative sections       |
| **`article`**             | Individual articles | `title`, `summary`, `fullContent` | Find specific guides or procedures |
| **`["node", "article"]`** | Both types          | All fields                        | Comprehensive search               |

### Advanced Search Behaviors

| Behavior             | Option                    | Effect                         | Example                      |
| -------------------- | ------------------------- | ------------------------------ | ---------------------------- |
| **Case Sensitivity** | `case_sensitive: true`    | Exact case matching required   | "W-2" ≠ "w-2"                |
| **Case Insensitive** | `case_sensitive: false`   | Ignores case differences       | "Employee" = "employee"      |
| **Whole Words**      | `whole_words_only: true`  | Matches complete words only    | "tax" won't match "taxation" |
| **Partial Words**    | `whole_words_only: false` | Allows partial word matches    | "pay" matches "payroll"      |
| **With Snippets**    | `include_snippets: true`  | Returns context around matches | Shows surrounding text       |
| **Without Snippets** | `include_snippets: false` | Faster execution, no context   | Results only                 |

## 📡 API Integration

### Complete API Search Options

The Advanced Search API at `/api/search/advanced` supports all search methods with comprehensive configuration:

```typescript
// POST /api/search/advanced - Complete Configuration
{
  "query": "employee benefits",
  "graph_name": "mock_hr",

  // === SEARCH METHODS ===
  "search_methods": {
    // Vector Similarity (Semantic Search)
    "vector_similarity": {
      "enabled": true,
      "weight": 0.7,
      "model": "voyage-3.5",
      "dimension": 1024
    },

    // Sparse Vectors (SPLADE - Keyword Matching)
    "sparse_vectors": {
      "enabled": true,
      "weight": 0.3
    },

    // Exact Phrase Search
    "exact_phrase": {
      "enabled": true,
      "weight": 0.4,
      "options": {
        "search_fields": ["title", "content", "summary", "fullContent"],
        "entity_types": ["both"], // "node", "article", or "both"
        "case_sensitive": false,
        "whole_words_only": false
      }
    },

    // Exact Word Search
    "exact_words": {
      "enabled": true,
      "weight": 0.2
    }
  },

  // === SEARCH MODES ===
  // Single Vector Mode (for dense or sparse only)
  "single_vector_mode": {
    "enabled": false,
    "vector_type": "dense" // "dense" or "sparse"
  },

  // === FUSION METHODS ===
  "fusion": {
    "method": {
      "type": "rrf", // "rrf", "dbsf", "manual_weighted", "multi_stage"
      "params": {
        "k": 2, // RRF constant
        "alpha": 0.5, // DBSF alpha parameter
        "vectorWeight": 0.9, // Manual weighted - semantic weight
        "sparseWeight": 0.1  // Manual weighted - keyword weight
      }
    },
    "enabled": true
  },

  // === MULTI-STAGE SEARCH ===
  "multi_stage": {
    "enabled": false,
    "stages": [
      { "prefetch_limit": 1000, "using": "dense", "query_type": "dense" },
      { "prefetch_limit": 100, "using": "dense", "query_type": "dense" }
    ]
  },

  // === HR-SPECIFIC FILTERS ===
  "filters": {
    "section_path": ["People Management", "HR Policies"],
    "article_type": "article", // "section" or "article"
    "level": 2,
    "update_date_after": "2024-01-01"
  },

  // === RESULT OPTIONS ===
  "result_options": {
    "limit": 10,
    "offset": 0,
    "include_snippets": true,
    "snippet_context_words": 15
  }
}
```

### Search Method Combinations

| Configuration         | Use Case                                          | Performance       | Best For                |
| --------------------- | ------------------------------------------------- | ----------------- | ----------------------- |
| **Vector Only**       | `vector_similarity: {enabled: true, weight: 1.0}` | Fast (~1s)        | Semantic understanding  |
| **Sparse Only**       | `sparse_vectors: {enabled: true, weight: 1.0}`    | Moderate (~2s)    | Keyword precision       |
| **Exact Phrase Only** | `exact_phrase: {enabled: true, weight: 1.0}`      | Very Fast (~0.2s) | Precise phrase matching |
| **Hybrid Balanced**   | `vector: 0.5, sparse: 0.5`                        | Moderate (~1.5s)  | Balanced results        |
| **Semantic Heavy**    | `vector: 0.9, sparse: 0.1`                        | Moderate (~4s)    | Conceptual search       |
| **Keyword Heavy**     | `vector: 0.2, sparse: 0.8`                        | Moderate (~1.5s)  | Precise terminology     |
| **Triple Method**     | `vector: 0.5, sparse: 0.3, exact_phrase: 0.2`     | Slower (~2s)      | Comprehensive coverage  |

### Fusion Method Guide

| Fusion Type         | When to Use                          | Configuration                              | Performance |
| ------------------- | ------------------------------------ | ------------------------------------------ | ----------- |
| **RRF**             | Balanced weights (≤0.1 difference)   | `"type": "rrf", "params": {"k": 2}`        | Fast        |
| **Manual Weighted** | Unbalanced weights (>0.1 difference) | `"type": "manual_weighted"`                | Moderate    |
| **DBSF**            | Advanced score distribution          | `"type": "dbsf", "params": {"alpha": 0.5}` | Fast        |
| **Multi-Stage**     | Progressive refinement               | `"type": "multi_stage"`                    | Variable    |

### cURL Examples

#### Search All Fields

```bash
curl -X POST http://localhost:3000/api/search/advanced \
  -H "Content-Type: application/json" \
  -d '{
    "query": "employee handbook",
    "graph_name": "mock_hr",
    "search_methods": {
      "exact_phrase": {
        "enabled": true,
        "weight": 1.0
      }
    },
    "result_options": {
      "limit": 5
    }
  }'
```

#### Search Only Article Summaries

```bash
curl -X POST http://localhost:3000/api/search/advanced \
  -H "Content-Type: application/json" \
  -d '{
    "query": "paystub information",
    "graph_name": "mock_hr",
    "search_methods": {
      "exact_phrase": {
        "enabled": true,
        "weight": 1.0,
        "options": {
          "search_fields": ["summary"],
          "entity_types": ["article"]
        }
      }
    },
    "result_options": {
      "limit": 3,
      "include_snippets": true
    }
  }'
```

#### Search Only Node Content (Categories)

```bash
curl -X POST http://localhost:3000/api/search/advanced \
  -H "Content-Type: application/json" \
  -d '{
    "query": "payroll management",
    "graph_name": "mock_hr",
    "search_methods": {
      "exact_phrase": {
        "enabled": true,
        "weight": 1.0,
        "options": {
          "search_fields": ["content"],
          "entity_types": ["node"]
        }
      }
    },
    "result_options": {
      "limit": 5
    }
  }'
```

#### Case-Sensitive Search

```bash
curl -X POST http://localhost:3000/api/search/advanced \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Form I-9",
    "graph_name": "mock_hr",
    "search_methods": {
      "exact_phrase": {
        "enabled": true,
        "weight": 1.0,
        "options": {
          "case_sensitive": true,
          "search_fields": ["title", "fullContent"]
        }
      }
    },
    "result_options": {
      "limit": 10
    }
  }'
```

## 🎯 Field-Specific Search Strategies

### By Content Type

| Search Target             | Fields            | Entity Types  | Use Case                            |
| ------------------------- | ----------------- | ------------- | ----------------------------------- |
| **Category Names**        | `["title"]`       | `["node"]`    | Find section/category titles        |
| **Category Descriptions** | `["content"]`     | `["node"]`    | Find detailed category descriptions |
| **Article Titles**        | `["title"]`       | `["article"]` | Find specific article names         |
| **Article Summaries**     | `["summary"]`     | `["article"]` | Find brief article descriptions     |
| **Full Article Content**  | `["fullContent"]` | `["article"]` | Find detailed article text          |

### By Use Case

| Use Case                 | Configuration                       | Example              |
| ------------------------ | ----------------------------------- | -------------------- |
| **Find any mention**     | All fields, both types              | "employee benefits"  |
| **Find section names**   | `title` field, `node` type          | "payroll management" |
| **Find detailed guides** | `fullContent` field, `article` type | "tax filing process" |
| **Find quick summaries** | `summary` field, `article` type     | "onboarding steps"   |

## 📊 Response Format

```typescript
interface HybridSearchResult {
  success: boolean;
  query: string;
  collection_name: string;
  total_results: number;
  execution_time_ms: number;
  results: SearchResult[];
  fusion_info?: {
    method: { type: "exact_phrase" };
    combined_results: number;
    unique_results: number;
  };
}

interface SearchResult {
  record: HRSearchRecord;
  relevance_score: number;
  method_contributions: {
    exact_phrase: number;
  };
  snippets?: SearchSnippet[];
}

interface SearchSnippet {
  text: string; // The snippet text
  context_before: string; // Text before the phrase
  context_after: string; // Text after the phrase
  start_position: number; // Position in original text
  end_position: number; // End position in original text
  relevance_score: number; // Snippet relevance (1.0 for exact match)
}
```

## 🔄 Integration with Hybrid Search

### Combine with Semantic Search

```typescript
{
  "search_methods": {
    "vector_similarity": {
      "enabled": true,
      "weight": 0.7
    },
    "exact_phrase": {
      "enabled": true,
      "weight": 0.3
    }
  },
  "fusion": {
    "method": { "type": "rrf", "params": { "k": 2 } },
    "enabled": true
  }
}
```

### Combine with Sparse Vectors (SPLADE)

```typescript
{
  "search_methods": {
    "sparse_vectors": {
      "enabled": true,
      "weight": 0.5
    },
    "exact_phrase": {
      "enabled": true,
      "weight": 0.5
    }
  },
  "fusion": {
    "method": { "type": "manual_weighted" },
    "enabled": true
  }
}
```

## ⚡ Performance Characteristics

| Metric              | Value        | Notes                                            |
| ------------------- | ------------ | ------------------------------------------------ |
| **Query Speed**     | ~240ms       | For exact phrase search across all fields        |
| **Index Creation**  | ~1-2 seconds | One-time setup per collection                    |
| **Memory Overhead** | Minimal      | Qdrant handles index optimization                |
| **Scalability**     | Excellent    | Leverages Qdrant's native full-text capabilities |

## 🧪 Test Results

Based on successful API testing with HR data:

| Query                   | Fields Searched   | Results   | Status                              |
| ----------------------- | ----------------- | --------- | ----------------------------------- |
| `"gross pay"`           | All fields        | 1 match   | ✅ Found in paystub content         |
| `"payroll"`             | Node content only | 1 match   | ✅ Found in admin section           |
| `"Form I-9"`            | All fields        | 1 match   | ✅ Found in compliance article      |
| `"employee"`            | All fields        | 6 matches | ✅ Distributed across content types |
| `"paystub information"` | Summary only      | 0 matches | ✅ Correctly returns no results     |

## 🛠️ Advanced Features

### Custom Relevance Scoring

The implementation uses a sophisticated scoring algorithm:

- **Frequency Score** (40%): Number of phrase occurrences
- **Position Score** (30%): Earlier matches score higher
- **Length Score** (30%): Shorter content with matches scores higher

### Snippet Extraction

- **Context-aware**: Extracts text around phrase matches
- **Configurable context**: Adjust words before/after match
- **Duplicate prevention**: Avoids repeated snippets
- **Smart boundaries**: Respects word boundaries

### Error Handling

- **Graceful degradation**: Returns empty results on errors
- **Index validation**: Checks for required indexes
- **Connection resilience**: Handles Qdrant connection issues

## 🔍 Troubleshooting

### Common Issues

1. **No results found**

   - Verify indexes are created: `await createFullTextIndexes(collection)`
   - Check field names match your data structure
   - Verify phrase exists in specified fields

2. **Poor performance**

   - Reduce `snippet_context_words` for faster snippet extraction
   - Limit `search_fields` to necessary fields only
   - Use smaller `limit` values

3. **Missing snippets**
   - Ensure `include_snippets: true` in options
   - Check that matched content has sufficient context
   - Verify `snippet_context_words` is appropriate

### Debug Mode

```typescript
const results = await engine.exactPhraseMatch("test phrase", "mock_hr", {
  search_fields: ["title", "content"],
  include_snippets: true,
  limit: 5,
});

console.log("Search results:", results);
console.log("Execution time:", results.execution_time_ms + "ms");
console.log("Total matches:", results.total_results);
```

## 📝 Best Practices

1. **Field Selection**: Choose specific fields when possible for better performance
2. **Entity Filtering**: Use `entity_types` to narrow search scope
3. **Case Sensitivity**: Use `case_sensitive: true` for exact matches (e.g., "W-2")
4. **Snippet Context**: Adjust `snippet_context_words` based on content type
5. **Hybrid Integration**: Combine with semantic search for comprehensive results

## 🧪 Testing the API

### 1. Start the Development Server

```bash
# Using pnpm (recommended)
pnpm dev

# Using npm
npm run dev

# Using yarn
yarn dev
```

The server will start on `http://localhost:3000`

### 2. Test with cURL

#### Basic Exact Phrase Search

```bash
curl -X POST http://localhost:3000/api/search/advanced \
  -H "Content-Type: application/json" \
  -d '{
    "query": "time off",
    "graph_name": "mock_hr",
    "search_methods": {
      "exact_phrase": {
        "enabled": true,
        "weight": 1.0
      }
    },
    "result_options": {
      "limit": 5,
      "include_snippets": true
    }
  }'
```

#### Hybrid Search (Semantic + Exact Phrase)

```bash
curl -X POST http://localhost:3000/api/search/advanced \
  -H "Content-Type: application/json" \
  -d '{
    "query": "employee benefits",
    "graph_name": "mock_hr",
    "search_methods": {
      "vector_similarity": {
        "enabled": true,
        "weight": 0.7
      },
      "exact_phrase": {
        "enabled": true,
        "weight": 0.3
      }
    },
    "result_options": {
      "limit": 10,
      "include_snippets": true
    }
  }'
```

#### Manual Weighted Fusion (90% Semantic, 10% Keyword)

```bash
curl -X POST http://localhost:3000/api/search/advanced \
  -H "Content-Type: application/json" \
  -d '{
    "query": "training program",
    "graph_name": "mock_hr",
    "search_methods": {
      "vector_similarity": {
        "enabled": true,
        "weight": 0.9
      },
      "sparse_vectors": {
        "enabled": true,
        "weight": 0.1
      }
    },
    "result_options": {
      "limit": 5
    }
  }'
```

#### DBSF Fusion Search

```bash
curl -X POST http://localhost:3000/api/search/advanced \
  -H "Content-Type: application/json" \
  -d '{
    "query": "company culture",
    "graph_name": "mock_hr",
    "search_methods": {
      "vector_similarity": {
        "enabled": true,
        "weight": 0.6
      },
      "sparse_vectors": {
        "enabled": true,
        "weight": 0.4
      }
    },
    "fusion": {
      "method": {
        "type": "dbsf"
      },
      "enabled": true
    },
    "result_options": {
      "limit": 8
    }
  }'
```

#### Single Vector Mode (Dense Only)

```bash
curl -X POST http://localhost:3000/api/search/advanced \
  -H "Content-Type: application/json" \
  -d '{
    "query": "performance review",
    "graph_name": "mock_hr",
    "search_methods": {},
    "single_vector_mode": {
      "enabled": true,
      "vector_type": "dense"
    },
    "result_options": {
      "limit": 5
    }
  }'
```

#### With HR Filters

```bash
curl -X POST http://localhost:3000/api/search/advanced \
  -H "Content-Type: application/json" \
  -d '{
    "query": "policy updates",
    "graph_name": "mock_hr",
    "search_methods": {
      "vector_similarity": {
        "enabled": true,
        "weight": 0.7
      },
      "sparse_vectors": {
        "enabled": true,
        "weight": 0.3
      }
    },
    "filters": {
      "article_type": "article",
      "level": 1
    },
    "result_options": {
      "limit": 5
    }
  }'
```

### 3. Automated Testing

#### Run Comprehensive Test Suite

```bash
# Test all search methods and configurations
npx tsx advancedSearchAPI/test-comprehensive-api.ts

# Test API integration specifically
npx tsx advancedSearchAPI/test-api-integration.ts

# Test manual fusion specifically
npx tsx advancedSearchAPI/test-manual-fusion.ts
```

#### Individual Test Commands

```bash
# Test exact phrase search
npx tsx -e "
import { exactPhraseSearchAll } from './advancedSearchAPI/hybrid-search';
const results = await exactPhraseSearchAll('time off');
console.log('Results:', results.results.length);
console.log('Top result:', results.results[0]?.record.title);
"

# Test hybrid search
npx tsx -e "
import { hrHybridSearch } from './advancedSearchAPI/hybrid-search';
const results = await hrHybridSearch('employee benefits');
console.log('Results:', results.results.length);
console.log('Execution time:', results.execution_time_ms + 'ms');
"
```

### 4. Expected Response Format

```json
{
  "success": true,
  "query": "time off",
  "graph_name": "mock_hr",
  "collection_name": "mock_hr",
  "search_mode": "fusion",
  "total_results": 3,
  "execution_time_ms": 239,
  "results": [
    {
      "record": {
        "id": "84",
        "title": "Time Off and Holidays",
        "content": "Learn how to set up and manage time off policies...",
        "summary": "",
        "fullContent": "",
        "updateDate": "",
        "breadcrumb": "",
        "section_path": ["People Management"],
        "article_type": "section",
        "level": 1,
        "nodeType": "category",
        "country": "",
        "htmlUrl": ""
      },
      "relevance_score": 1.0,
      "method_contributions": {
        "exact_phrase": 1.0
      },
      "snippets": [
        {
          "text": "Learn how to set up and manage time off policies, including vacation, sick leave, and company holidays.",
          "context_before": "",
          "context_after": "This section covers policy creation, tracking accruals, and approving time off requests from your team.",
          "start_position": 0,
          "end_position": 102,
          "relevance_score": 1.0
        }
      ]
    }
  ],
  "fusion_info": {
    "method": {
      "type": "exact_phrase"
    },
    "combined_results": 1,
    "unique_results": 3
  }
}
```

### 5. Performance Benchmarks

Based on test results with mock HR data:

| Search Type           | Avg Time | Results      | Success Rate |
| --------------------- | -------- | ------------ | ------------ |
| **Exact Phrase**      | ~240ms   | 1-3 results  | 100%         |
| **Vector Similarity** | ~1.1s    | 5-10 results | 100%         |
| **Sparse Vectors**    | ~25s     | 5-10 results | 100%         |
| **Manual Weighted**   | ~4.1s    | 5-8 results  | 100%         |
| **DBSF Fusion**       | ~1.2s    | 5-10 results | 100%         |
| **Multi-Stage**       | ~0.5s    | 5-10 results | 100%         |

### 6. Troubleshooting Tests

#### Common Test Issues

1. **Server not running**

   ```bash
   # Check if server is running
   curl http://localhost:3000/api/search/advanced
   # Should return method not allowed (405) not connection refused
   ```

2. **Environment variables missing**

   ```bash
   # Check .env.local file exists with:
   # QDRANT_URL=your_qdrant_url
   # QDRANT_API_KEY=your_api_key
   # VOYAGE_API_KEY=your_voyage_key
   ```

3. **No results found**
   ```bash
   # Test with known phrases from mock data
   curl -X POST http://localhost:3000/api/search/advanced \
     -H "Content-Type: application/json" \
     -d '{"query": "time off", "graph_name": "mock_hr", "search_methods": {"exact_phrase": {"enabled": true, "weight": 1.0}}}'
   ```

#### Debug Mode Testing

```bash
# Enable debug logging
DEBUG=1 npx tsx advancedSearchAPI/test-comprehensive-api.ts

# Test specific search method
npx tsx -e "
process.env.DEBUG = '1';
import { createHybridSearchEngine } from './advancedSearchAPI/hybrid-search';
const engine = createHybridSearchEngine();
const result = await engine.exactPhraseMatch('time off', 'mock_hr');
console.log(JSON.stringify(result, null, 2));
"
```

## 🔗 Related Documentation

- [Hybrid Search Implementation](./README.md)
- [Qdrant Client Configuration](./qdrant-client.ts)
- [SPLADE Sparse Vector Guide](./SPLADE-IMPLEMENTATION-SUMMARY.md)
- [API Endpoint Documentation](../app/api/search/advanced/route.ts)
- [Comprehensive Test Suite](./test-comprehensive-api.ts)
- [API Integration Tests](./test-api-integration.ts)
