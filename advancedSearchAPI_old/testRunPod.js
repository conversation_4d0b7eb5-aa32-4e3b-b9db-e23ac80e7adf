async function main() {
  const url = "https://api.runpod.ai/v2/i57fast0l4w9ig/runsync";

  const requestConfig = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization:
        "Bearer rpa_4JYWJPKG4PNCXD2OMUQUEQFOJOTESKFDV6T5EXA8wabhjd",
    },
    body: JSON.stringify({
      input: {
        model: "naver/splade-v3",
        input: "This is a sentence. Yolooo",
      },
    }),
  };

  try {
    const response = await fetch(url, requestConfig);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log(data);
    return data;
  } catch (error) {
    console.error("Error:", error);
    throw error;
  }
}

// Execute the function
main()
  .then((result) => console.log("Success:", result))
  .catch((error) => console.error("Error:", error));
