#!/usr/bin/env node

const https = require("https");

// API request payload
const requestData = {
  query: "paystub",
  graph_name: "mock_hr",
  search_methods: {
    vector_similarity: { enabled: true, weight: 0.2 },
    sparse_vectors: { enabled: true, weight: 0.8 },
  },
  filters: {
    //section_path: ["Workplace Policies"],
    article_type: "article",
  },
  result_options: {
    limit: 10,
    include_snippets: true,
    snippet_context_words: 15,
  },
};

const options = {
  hostname:
    "internal-kbtreev2-rd-git-new-0b138c-devs-powertothebrs-projects.vercel.app",
  port: 443,
  path: "/api/search/advanced",
  method: "POST",
  headers: {
    "Content-Type": "application/json",
    "Content-Length": Buffer.byteLength(JSON.stringify(requestData)),
  },
};

console.log("🚀 Executing Advanced Search API Test...");
console.log("📡 Endpoint:", `https://${options.hostname}${options.path}`);
console.log("🔍 Query:", requestData.query);
console.log("📊 Search Methods:", Object.keys(requestData.search_methods));
console.log("⏱️  Starting request...\n");

const startTime = Date.now();

const req = https.request(options, (res) => {
  const endTime = Date.now();
  const responseTime = endTime - startTime;

  console.log("📈 Response Status:", res.statusCode);
  console.log("⏱️  Response Time:", `${responseTime}ms`);
  console.log("🔧 Response Headers:");
  Object.entries(res.headers).forEach(([key, value]) => {
    console.log(`   ${key}: ${value}`);
  });
  console.log("\n" + "=".repeat(60));

  let data = "";

  res.on("data", (chunk) => {
    data += chunk;
  });

  res.on("end", () => {
    try {
      const response = JSON.parse(data);
      analyzeResponse(response, responseTime);
    } catch (error) {
      console.error("❌ Failed to parse JSON response:");
      console.error("Raw response:", data);
      console.error("Parse error:", error.message);
    }
  });
});

req.on("error", (error) => {
  console.error("❌ Request failed:", error.message);
  console.error("🔧 Troubleshooting tips:");
  console.error("   - Check internet connection");
  console.error("   - Verify the API endpoint is accessible");
  console.error("   - Check if the service is running");
});

// Send the request
req.write(JSON.stringify(requestData));
req.end();

function analyzeResponse(response, responseTime) {
  console.log("📊 RESPONSE ANALYSIS");
  console.log("=".repeat(60));

  // Basic response info
  console.log("✅ Success:", response.success || "N/A");
  console.log("🔍 Query:", response.query || "N/A");
  console.log("📚 Collection:", response.collection_name || "N/A");
  console.log("🔄 Search Mode:", response.search_mode || "N/A");
  console.log("📊 Total Results:", response.total_results || 0);
  console.log(
    "⏱️  Execution Time:",
    response.execution_time_ms ? `${response.execution_time_ms}ms` : "N/A"
  );
  console.log("🌐 Network Time:", `${responseTime}ms`);

  if (response.error) {
    console.log("\n❌ ERROR DETAILS");
    console.log("=".repeat(30));
    console.log("Error:", response.error);
    if (response.details) console.log("Details:", response.details);
    if (response.debug) {
      console.log("Debug Info:", JSON.stringify(response.debug, null, 2));
    }
    return;
  }

  if (response.results && response.results.length > 0) {
    console.log("\n📋 RESULTS BREAKDOWN");
    console.log("=".repeat(30));
    console.log("Results returned:", response.results.length);

    // Analyze result scores
    const scores = response.results.map((r) => r.relevance_score || 0);
    const avgScore = scores.reduce((a, b) => a + b, 0) / scores.length;
    const maxScore = Math.max(...scores);
    const minScore = Math.min(...scores);

    console.log("Score Analysis:");
    console.log(`   Average: ${avgScore.toFixed(3)}`);
    console.log(`   Highest: ${maxScore.toFixed(3)}`);
    console.log(`   Lowest: ${minScore.toFixed(3)}`);

    // Analyze method contributions
    if (response.results[0].method_contributions) {
      console.log("\n🔬 METHOD CONTRIBUTIONS (Top Result)");
      console.log("=".repeat(40));
      const contributions = response.results[0].method_contributions;
      Object.entries(contributions).forEach(([method, score]) => {
        console.log(
          `   ${method}: ${
            typeof score === "number" ? score.toFixed(3) : score
          }`
        );
      });
    }

    // Show top results
    console.log("\n🏆 TOP 3 RESULTS");
    console.log("=".repeat(25));
    response.results.slice(0, 3).forEach((result, index) => {
      console.log(`\n${index + 1}. ${result.record?.title || "No title"}`);
      console.log(`   Score: ${result.relevance_score?.toFixed(3) || "N/A"}`);
      console.log(
        `   Type: ${
          result.record?.nodeType || result.record?.article_type || "N/A"
        }`
      );
      if (result.record?.breadcrumb) {
        console.log(`   Path: ${result.record.breadcrumb}`);
      }
      if (result.snippets && result.snippets.length > 0) {
        console.log(
          `   Snippet: "${result.snippets[0].text?.substring(0, 100)}..."`
        );
      }
    });
  } else {
    console.log("\n📭 NO RESULTS FOUND");
    console.log("Possible reasons:");
    console.log("   - Knowledge base is empty");
    console.log("   - Query terms not found");
    console.log("   - Filters too restrictive");
    console.log("   - Search service not properly configured");
  }

  // Fusion analysis
  if (response.fusion_info) {
    console.log("\n🔀 FUSION ANALYSIS");
    console.log("=".repeat(25));
    console.log("Method:", response.fusion_info.method?.type || "N/A");
    if (response.fusion_info.method?.params) {
      console.log(
        "Params:",
        JSON.stringify(response.fusion_info.method.params, null, 2)
      );
    }
    console.log(
      "Combined Results:",
      response.fusion_info.combined_results || "N/A"
    );
    console.log(
      "Unique Results:",
      response.fusion_info.unique_results || "N/A"
    );
  }

  console.log("\n" + "=".repeat(60));
  console.log("✨ Analysis Complete!");
}
