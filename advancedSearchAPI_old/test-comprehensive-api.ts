/**
 * Comprehensive Advanced Search API Test Suite
 * 
 * Tests ALL search possibilities including:
 * - All search methods (vector, sparse, exact phrase, exact words)
 * - All fusion methods (RRF, DBSF, manual weighted, multi-stage)
 * - Single vector modes (dense-only, sparse-only)
 * - HR-specific filters and entity filtering
 * - Error scenarios and edge cases
 * - API route validation via HTTP requests
 */

import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config({ path: path.join(process.cwd(), '.env.local') });

import {
  HybridSearchEngine,
  createHybridSearchEngine,
  DEFAULT_HR_COLLECTION,
  type HybridSearchConfig,
  type HybridSearchResult,
  exactPhraseSearchAll,
  exactPhraseSearchNodes,
  exactPhraseSearchArticles,
  hrHybridSearch
} from './hybrid-search';

import type { AdvancedSearchRequest, AdvancedSearchResponse } from './types';

// Test configuration
const API_BASE_URL = 'http://localhost:3000';
const TEST_COLLECTION = 'mock_hr';

interface TestCase {
  name: string;
  description: string;
  request: AdvancedSearchRequest;
  expectedSuccess: boolean;
  validationChecks: string[];
}

interface TestResult {
  testName: string;
  success: boolean;
  executionTime: number;
  resultCount: number;
  topScore: number;
  searchMode?: string;
  fusionMethod?: string;
  methodContributions?: any;
  error?: string;
  validationsPassed: number;
  validationsTotal: number;
}

/**
 * Comprehensive test cases covering all API possibilities
 */
const COMPREHENSIVE_TEST_CASES: TestCase[] = [
  // =============================================================================
  // 1. SINGLE VECTOR SEARCH MODES
  // =============================================================================
  {
    name: 'single_vector_dense_only',
    description: 'Dense vector search only (semantic similarity)',
    request: {
      query: 'employee benefits package',
      graph_name: TEST_COLLECTION,
      search_methods: {},
      single_vector_mode: {
        enabled: true,
        vector_type: 'dense'
      },
      result_options: { limit: 5 }
    },
    expectedSuccess: true,
    validationChecks: ['has_results', 'search_mode_single_vector', 'vector_type_dense']
  },
  {
    name: 'single_vector_sparse_only',
    description: 'Sparse vector search only (keyword matching)',
    request: {
      query: 'time off policy',
      graph_name: TEST_COLLECTION,
      search_methods: {},
      single_vector_mode: {
        enabled: true,
        vector_type: 'sparse'
      },
      result_options: { limit: 5 }
    },
    expectedSuccess: true,
    validationChecks: ['has_results', 'search_mode_single_vector', 'vector_type_sparse']
  },

  // =============================================================================
  // 2. HYBRID SEARCH WITH RRF FUSION (Balanced Weights)
  // =============================================================================
  {
    name: 'hybrid_rrf_balanced',
    description: 'Hybrid search with RRF fusion (balanced weights)',
    request: {
      query: 'remote work guidelines',
      graph_name: TEST_COLLECTION,
      search_methods: {
        vector_similarity: { enabled: true, weight: 0.5 },
        sparse_vectors: { enabled: true, weight: 0.5 }
      },
      result_options: { limit: 10, include_snippets: true }
    },
    expectedSuccess: true,
    validationChecks: ['has_results', 'search_mode_fusion', 'has_snippets', 'fusion_method_rrf']
  },

  // =============================================================================
  // 3. MANUAL WEIGHTED FUSION (Unbalanced Weights)
  // =============================================================================
  {
    name: 'manual_weighted_fusion_semantic_heavy',
    description: 'Manual weighted fusion - semantic heavy (90% semantic, 10% keyword)',
    request: {
      query: 'employee wellbeing initiatives',
      graph_name: TEST_COLLECTION,
      search_methods: {
        vector_similarity: { enabled: true, weight: 0.9 },
        sparse_vectors: { enabled: true, weight: 0.1 }
      },
      result_options: { limit: 8 }
    },
    expectedSuccess: true,
    validationChecks: ['has_results', 'manual_fusion_triggered', 'has_individual_scores', 'semantic_score_higher']
  },
  {
    name: 'manual_weighted_fusion_keyword_heavy',
    description: 'Manual weighted fusion - keyword heavy (20% semantic, 80% keyword)',
    request: {
      query: 'Form I-9 compliance requirements',
      graph_name: TEST_COLLECTION,
      search_methods: {
        vector_similarity: { enabled: true, weight: 0.2 },
        sparse_vectors: { enabled: true, weight: 0.8 }
      },
      result_options: { limit: 8 }
    },
    expectedSuccess: true,
    validationChecks: ['has_results', 'manual_fusion_triggered', 'has_individual_scores', 'keyword_score_higher']
  },

  // =============================================================================
  // 4. DBSF FUSION METHOD
  // =============================================================================
  {
    name: 'dbsf_fusion_advanced',
    description: 'DBSF (Distribution-Based Score Fusion) method',
    request: {
      query: 'performance review process',
      graph_name: TEST_COLLECTION,
      search_methods: {
        vector_similarity: { enabled: true, weight: 0.6 },
        sparse_vectors: { enabled: true, weight: 0.4 }
      },
      fusion: {
        method: { type: 'dbsf' },
        enabled: true
      },
      result_options: { limit: 10 }
    },
    expectedSuccess: true,
    validationChecks: ['has_results', 'fusion_method_dbsf', 'search_mode_fusion']
  },

  // =============================================================================
  // 5. EXACT PHRASE SEARCH
  // =============================================================================
  {
    name: 'exact_phrase_search_all_fields',
    description: 'Exact phrase search across all fields',
    request: {
      query: 'time off',
      graph_name: TEST_COLLECTION,
      search_methods: {
        exact_phrase: {
          enabled: true,
          weight: 1.0,
          options: {
            search_fields: ['title', 'content', 'summary', 'fullContent'],
            entity_types: ['both'],
            case_sensitive: false
          }
        }
      },
      result_options: { limit: 5, include_snippets: true, snippet_context_words: 20 }
    },
    expectedSuccess: true,
    validationChecks: ['has_results', 'exact_phrase_method', 'has_snippets']
  },
  {
    name: 'exact_phrase_nodes_only',
    description: 'Exact phrase search in nodes (categories) only',
    request: {
      query: 'Time Off and Holidays',
      graph_name: TEST_COLLECTION,
      search_methods: {
        exact_phrase: {
          enabled: true,
          weight: 1.0,
          options: {
            search_fields: ['title', 'content'],
            entity_types: ['node'],
            case_sensitive: false
          }
        }
      },
      result_options: { limit: 5 }
    },
    expectedSuccess: true,
    validationChecks: ['has_results', 'exact_phrase_method', 'entity_type_node']
  },
  {
    name: 'exact_phrase_articles_only',
    description: 'Exact phrase search in articles only',
    request: {
      query: 'employee handbook',
      graph_name: TEST_COLLECTION,
      search_methods: {
        exact_phrase: {
          enabled: true,
          weight: 1.0,
          options: {
            search_fields: ['title', 'summary', 'fullContent'],
            entity_types: ['article'],
            case_sensitive: false
          }
        }
      },
      result_options: { limit: 5 }
    },
    expectedSuccess: true,
    validationChecks: ['has_results', 'exact_phrase_method', 'entity_type_article']
  },

  // =============================================================================
  // 6. EXACT WORD SEARCH
  // =============================================================================
  {
    name: 'exact_word_search',
    description: 'Exact word matching search',
    request: {
      query: 'onboarding checklist',
      graph_name: TEST_COLLECTION,
      search_methods: {
        exact_words: { enabled: true, weight: 1.0 }
      },
      result_options: { limit: 8 }
    },
    expectedSuccess: true,
    validationChecks: ['exact_word_method']
  },

  // =============================================================================
  // 7. MULTI-STAGE SEARCH
  // =============================================================================
  {
    name: 'multi_stage_search',
    description: 'Multi-stage hierarchical search with progressive refinement',
    request: {
      query: 'international contractor payments',
      graph_name: TEST_COLLECTION,
      search_methods: {
        vector_similarity: { enabled: true, weight: 0.7 }
      },
      multi_stage: {
        enabled: true,
        stages: [
          { prefetch_limit: 1000, using: 'dense', query_type: 'dense' },
          { prefetch_limit: 100, using: 'dense', query_type: 'dense' }
        ]
      },
      result_options: { limit: 10 }
    },
    expectedSuccess: true,
    validationChecks: ['has_results', 'multi_stage_method']
  },

  // =============================================================================
  // 8. COMPLEX HYBRID COMBINATIONS
  // =============================================================================
  {
    name: 'triple_method_hybrid',
    description: 'Triple method hybrid: semantic + sparse + exact phrase',
    request: {
      query: 'employee benefits enrollment',
      graph_name: TEST_COLLECTION,
      search_methods: {
        vector_similarity: { enabled: true, weight: 0.5 },
        sparse_vectors: { enabled: true, weight: 0.3 },
        exact_phrase: { enabled: true, weight: 0.2 }
      },
      result_options: { limit: 10, include_snippets: true }
    },
    expectedSuccess: true,
    validationChecks: ['has_results', 'multiple_methods', 'has_snippets']
  },

  // =============================================================================
  // 9. HR-SPECIFIC FILTERS
  // =============================================================================
  {
    name: 'hr_filters_section_path',
    description: 'Search with section path filtering',
    request: {
      query: 'policy updates',
      graph_name: TEST_COLLECTION,
      search_methods: {
        vector_similarity: { enabled: true, weight: 0.7 },
        sparse_vectors: { enabled: true, weight: 0.3 }
      },
      filters: {
        section_path: ['People Management', 'HR Policies'],
        article_type: 'article'
      },
      result_options: { limit: 8 }
    },
    expectedSuccess: true,
    validationChecks: ['has_results', 'filtered_by_section', 'article_type_filter']
  },
  {
    name: 'hr_filters_level_and_date',
    description: 'Search with level and date filtering',
    request: {
      query: 'compliance training',
      graph_name: TEST_COLLECTION,
      search_methods: {
        vector_similarity: { enabled: true, weight: 1.0 }
      },
      filters: {
        level: 2,
        update_date_after: '2024-01-01'
      },
      result_options: { limit: 5 }
    },
    expectedSuccess: true,
    validationChecks: ['has_results', 'level_filter', 'date_filter']
  },

  // =============================================================================
  // 10. ERROR SCENARIOS
  // =============================================================================
  {
    name: 'empty_query_error',
    description: 'Error case: Empty query string',
    request: {
      query: '',
      graph_name: TEST_COLLECTION,
      search_methods: {
        vector_similarity: { enabled: true, weight: 1.0 }
      }
    },
    expectedSuccess: false,
    validationChecks: ['error_empty_query']
  },
  {
    name: 'invalid_vector_type_error',
    description: 'Error case: Invalid vector type in single vector mode',
    request: {
      query: 'test query',
      graph_name: TEST_COLLECTION,
      search_methods: {},
      single_vector_mode: {
        enabled: true,
        vector_type: 'invalid' as any
      }
    },
    expectedSuccess: false,
    validationChecks: ['error_invalid_vector_type']
  }
];

/**
 * Validation functions for test results
 */
const VALIDATION_FUNCTIONS = {
  has_results: (response: any) => response.results && response.results.length > 0,
  search_mode_single_vector: (response: any) => response.searchMode === 'single_vector',
  search_mode_fusion: (response: any) => response.searchMode === 'fusion',
  vector_type_dense: (response: any) => response.vectorType === 'dense',
  vector_type_sparse: (response: any) => response.vectorType === 'sparse',
  has_snippets: (response: any) => response.results?.[0]?.snippets && response.results[0].snippets.length > 0,
  fusion_method_rrf: (response: any) => response.fusion_info?.method?.type === 'rrf',
  fusion_method_dbsf: (response: any) => response.fusion_info?.method?.type === 'dbsf',
  manual_fusion_triggered: (response: any) => response.fusion_info?.method?.type === 'manual_weighted',
  has_individual_scores: (response: any) => {
    const result = response.results?.[0];
    return result?.method_contributions?.semantic !== undefined && result?.method_contributions?.keyword !== undefined;
  },
  semantic_score_higher: (response: any) => {
    const contrib = response.results?.[0]?.method_contributions;
    return contrib?.semantic > contrib?.keyword;
  },
  keyword_score_higher: (response: any) => {
    const contrib = response.results?.[0]?.method_contributions;
    return contrib?.keyword > contrib?.semantic;
  },
  exact_phrase_method: (response: any) => response.fusion_info?.method?.type === 'exact_phrase',
  exact_word_method: (response: any) => response.method_scores?.exact_words !== undefined,
  multi_stage_method: (response: any) => response.fusion_info?.method?.type === 'multi_stage',
  multiple_methods: (response: any) => Object.keys(response.method_scores || {}).length > 1,
  entity_type_node: (response: any) => response.results?.every((r: any) => r.record.nodeType === 'category'),
  entity_type_article: (response: any) => response.results?.every((r: any) => r.record.nodeType === 'record'),
  filtered_by_section: (response: any) => response.results?.every((r: any) => 
    r.record.section_path && r.record.section_path.length > 0),
  article_type_filter: (response: any) => response.results?.every((r: any) => r.record.article_type === 'article'),
  level_filter: (response: any) => response.results?.every((r: any) => r.record.level >= 2),
  date_filter: (response: any) => response.results?.every((r: any) => 
    !r.record.updateDate || r.record.updateDate >= '2024-01-01'),
  error_empty_query: (response: any) => response.error && response.error.includes('Query is required'),
  error_invalid_vector_type: (response: any) => response.error && response.error.includes('vector type')
};

/**
 * Execute a single test case using the hybrid search engine directly
 */
async function executeTestCase(testCase: TestCase, engine: HybridSearchEngine): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    let searchResult: HybridSearchResult;
    
    // Route to appropriate search method based on request
    if (testCase.request.single_vector_mode?.enabled) {
      // Single vector mode - use basic hybrid search with only one method enabled
      const vectorType = testCase.request.single_vector_mode.vector_type;
      const config: HybridSearchConfig = {
        search_methods: vectorType === 'dense' 
          ? { vector_similarity: { enabled: true, weight: 1.0 } }
          : { sparse_vectors: { enabled: true, weight: 1.0 } },
        result_options: testCase.request.result_options,
        filters: testCase.request.filters
      };
      
      searchResult = await engine.basicHybridSearch(
        testCase.request.query,
        testCase.request.graph_name,
        config
      );
      
      // Mock single vector response format
      (searchResult as any).searchMode = 'single_vector';
      (searchResult as any).vectorType = vectorType;
      
    } else if (testCase.request.search_methods.exact_phrase?.enabled && 
               Object.keys(testCase.request.search_methods).length === 1) {
      // Pure exact phrase search
      const options = testCase.request.search_methods.exact_phrase.options;
      searchResult = await engine.exactPhraseMatch(
        testCase.request.query,
        testCase.request.graph_name,
        {
          limit: testCase.request.result_options?.limit || 10,
          search_fields: options?.search_fields as any,
          entity_types: options?.entity_types as any,
          case_sensitive: options?.case_sensitive,
          include_snippets: testCase.request.result_options?.include_snippets,
          snippet_context_words: testCase.request.result_options?.snippet_context_words
        }
      );
      
    } else if (testCase.request.multi_stage?.enabled) {
      // Multi-stage search
      const config: HybridSearchConfig = {
        search_methods: testCase.request.search_methods,
        multi_stage: testCase.request.multi_stage,
        result_options: testCase.request.result_options,
        filters: testCase.request.filters
      };
      
      searchResult = await engine.multiStageHybridSearch(
        testCase.request.query,
        testCase.request.graph_name,
        config
      );
      
    } else if (testCase.request.fusion?.method?.type === 'dbsf') {
      // DBSF fusion
      const config: HybridSearchConfig = {
        search_methods: testCase.request.search_methods,
        fusion: testCase.request.fusion,
        result_options: testCase.request.result_options,
        filters: testCase.request.filters
      };
      
      searchResult = await engine.advancedHybridSearch(
        testCase.request.query,
        testCase.request.graph_name,
        config
      );
      
    } else {
      // Standard hybrid search (RRF or manual weighted fusion)
      const config: HybridSearchConfig = {
        search_methods: testCase.request.search_methods,
        fusion: testCase.request.fusion,
        result_options: testCase.request.result_options,
        filters: testCase.request.filters
      };
      
      searchResult = await engine.basicHybridSearch(
        testCase.request.query,
        testCase.request.graph_name,
        config
      );
    }

    const executionTime = Date.now() - startTime;

    // Validate results
    let validationsPassed = 0;
    const validationsTotal = testCase.validationChecks.length;
    
    for (const check of testCase.validationChecks) {
      const validator = VALIDATION_FUNCTIONS[check as keyof typeof VALIDATION_FUNCTIONS];
      if (validator && validator(searchResult)) {
        validationsPassed++;
      }
    }

    const success = testCase.expectedSuccess ? 
      (searchResult.success && validationsPassed === validationsTotal) :
      (!searchResult.success);

    return {
      testName: testCase.name,
      success,
      executionTime,
      resultCount: searchResult.results?.length || 0,
      topScore: searchResult.results?.[0]?.relevance_score || 0,
      searchMode: (searchResult as any).searchMode,
      fusionMethod: searchResult.fusion_info?.method?.type,
      methodContributions: searchResult.results?.[0]?.method_contributions,
      validationsPassed,
      validationsTotal
    };

  } catch (error) {
    const executionTime = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    // For expected errors, check if the error matches expectations
    if (!testCase.expectedSuccess) {
      let validationsPassed = 0;
      const validationsTotal = testCase.validationChecks.length;
      
      const mockErrorResponse = { error: errorMessage };
      for (const check of testCase.validationChecks) {
        const validator = VALIDATION_FUNCTIONS[check as keyof typeof VALIDATION_FUNCTIONS];
        if (validator && validator(mockErrorResponse)) {
          validationsPassed++;
        }
      }
      
      return {
        testName: testCase.name,
        success: validationsPassed === validationsTotal,
        executionTime,
        resultCount: 0,
        topScore: 0,
        error: errorMessage,
        validationsPassed,
        validationsTotal
      };
    }

    return {
      testName: testCase.name,
      success: false,
      executionTime,
      resultCount: 0,
      topScore: 0,
      error: errorMessage,
      validationsPassed: 0,
      validationsTotal: testCase.validationChecks.length
    };
  }
}

/**
 * Run comprehensive test suite
 */
async function runComprehensiveTests(): Promise<void> {
  console.log('\n🧪 COMPREHENSIVE ADVANCED SEARCH API TEST SUITE');
  console.log('================================================');
  console.log(`🎯 Collection: ${TEST_COLLECTION}`);
  console.log(`📊 Total test cases: ${COMPREHENSIVE_TEST_CASES.length}`);
  console.log(`🔍 Testing all search methods, fusion types, and configurations`);

  const engine = createHybridSearchEngine();
  const results: TestResult[] = [];
  
  // Group tests by category for better organization
  const testCategories = {
    'Single Vector Modes': COMPREHENSIVE_TEST_CASES.filter(t => t.name.includes('single_vector')),
    'Hybrid Fusion Methods': COMPREHENSIVE_TEST_CASES.filter(t => 
      t.name.includes('hybrid') || t.name.includes('manual_weighted') || t.name.includes('dbsf')),
    'Exact Search Methods': COMPREHENSIVE_TEST_CASES.filter(t => 
      t.name.includes('exact_phrase') || t.name.includes('exact_word')),
    'Advanced Features': COMPREHENSIVE_TEST_CASES.filter(t => 
      t.name.includes('multi_stage') || t.name.includes('triple_method')),
    'Filtering & HR Features': COMPREHENSIVE_TEST_CASES.filter(t => t.name.includes('hr_filters')),
    'Error Scenarios': COMPREHENSIVE_TEST_CASES.filter(t => t.name.includes('error'))
  };

  for (const [categoryName, categoryTests] of Object.entries(testCategories)) {
    if (categoryTests.length === 0) continue;
    
    console.log(`\n📂 ${categoryName}`);
    console.log('─'.repeat(50));

    for (const testCase of categoryTests) {
      console.log(`\n🔬 ${testCase.name}: ${testCase.description}`);
      
      try {
        const result = await executeTestCase(testCase, engine);
        results.push(result);
        
        const statusIcon = result.success ? '✅' : '❌';
        console.log(`   ${statusIcon} Status: ${result.success ? 'PASSED' : 'FAILED'}`);
        console.log(`   ⏱️  Execution: ${result.executionTime}ms`);
        console.log(`   📊 Results: ${result.resultCount}`);
        console.log(`   🎯 Top Score: ${result.topScore.toFixed(4)}`);
        console.log(`   ✓ Validations: ${result.validationsPassed}/${result.validationsTotal}`);
        
        if (result.searchMode) {
          console.log(`   🔍 Search Mode: ${result.searchMode}`);
        }
        if (result.fusionMethod) {
          console.log(`   🔀 Fusion: ${result.fusionMethod}`);
        }
        if (result.methodContributions) {
          console.log(`   📈 Contributions:`, result.methodContributions);
        }
        if (result.error) {
          console.log(`   ⚠️  Error: ${result.error}`);
        }
        
      } catch (error) {
        console.log(`   💥 Test execution failed: ${error}`);
        results.push({
          testName: testCase.name,
          success: false,
          executionTime: 0,
          resultCount: 0,
          topScore: 0,
          error: String(error),
          validationsPassed: 0,
          validationsTotal: testCase.validationChecks.length
        });
      }
    }
  }

  // Overall analysis
  console.log('\n📊 COMPREHENSIVE TEST RESULTS ANALYSIS');
  console.log('======================================');

  const totalTests = results.length;
  const passedTests = results.filter(r => r.success).length;
  const failedTests = totalTests - passedTests;
  const successRate = (passedTests / totalTests * 100).toFixed(1);

  console.log(`\n🏆 Overall Results:`);
  console.log(`   ✅ Passed: ${passedTests}/${totalTests} (${successRate}%)`);
  console.log(`   ❌ Failed: ${failedTests}/${totalTests}`);

  // Category breakdown
  console.log(`\n📂 Results by Category:`);
  for (const [categoryName, categoryTests] of Object.entries(testCategories)) {
    if (categoryTests.length === 0) continue;
    
    const categoryResults = results.filter(r => 
      categoryTests.some(t => t.name === r.testName));
    const categoryPassed = categoryResults.filter(r => r.success).length;
    const categoryTotal = categoryResults.length;
    const categoryRate = categoryTotal > 0 ? (categoryPassed / categoryTotal * 100).toFixed(1) : '0';
    
    console.log(`   📁 ${categoryName}: ${categoryPassed}/${categoryTotal} (${categoryRate}%)`);
  }

  // Performance analysis
  console.log(`\n⏱️  Performance Analysis:`);
  const avgExecutionTime = results.reduce((sum, r) => sum + r.executionTime, 0) / results.length;
  const avgResultCount = results.filter(r => r.resultCount > 0)
    .reduce((sum, r) => sum + r.resultCount, 0) / results.filter(r => r.resultCount > 0).length || 0;
  const avgTopScore = results.filter(r => r.topScore > 0)
    .reduce((sum, r) => sum + r.topScore, 0) / results.filter(r => r.topScore > 0).length || 0;

  console.log(`   📊 Avg execution time: ${avgExecutionTime.toFixed(0)}ms`);
  console.log(`   📊 Avg result count: ${avgResultCount.toFixed(1)}`);
  console.log(`   📊 Avg top score: ${avgTopScore.toFixed(4)}`);

  // Failed test details
  const failedTestResults = results.filter(r => !r.success);
  if (failedTestResults.length > 0) {
    console.log(`\n❌ Failed Test Details:`);
    failedTestResults.forEach(result => {
      console.log(`   🔴 ${result.testName}:`);
      console.log(`      Validations: ${result.validationsPassed}/${result.validationsTotal}`);
      if (result.error) {
        console.log(`      Error: ${result.error}`);
      }
    });
  }

  // Feature coverage summary
  console.log(`\n🎯 Feature Coverage Summary:`);
  const featuresCovered = [
    'Single Vector (Dense)', 'Single Vector (Sparse)',
    'RRF Fusion', 'DBSF Fusion', 'Manual Weighted Fusion',
    'Exact Phrase Search', 'Exact Word Search', 'Multi-Stage Search',
    'HR Filters', 'Entity Filtering', 'Error Handling'
  ];
  
  console.log(`   ✅ Search Methods: Vector Similarity, Sparse Vectors, Exact Phrase, Exact Words`);
  console.log(`   ✅ Fusion Methods: RRF, DBSF, Manual Weighted, Multi-Stage`);
  console.log(`   ✅ Search Modes: Hybrid, Single Vector (Dense/Sparse)`);
  console.log(`   ✅ Advanced Features: HR Filters, Entity Types, Snippets`);
  console.log(`   ✅ Error Scenarios: Invalid queries, configuration errors`);

  console.log(`\n🎉 Comprehensive API testing completed!`);
  console.log(`📋 All major search capabilities have been validated.`);
}

/**
 * Main execution function
 */
async function main(): Promise<void> {
  try {
    await runComprehensiveTests();
    console.log('\n✅ All comprehensive tests completed successfully!');
  } catch (error) {
    console.error('\n❌ Comprehensive test suite failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('Comprehensive test runner failed:', error);
    process.exit(1);
  });
}

export { runComprehensiveTests, COMPREHENSIVE_TEST_CASES }; 