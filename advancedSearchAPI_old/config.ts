/**
 * Advanced Search API Configuration
 * 
 * Task 1.1: Qdrant Cloud Integration
 * Centralized configuration for environment variables, API settings, and defaults
 */

import dotenv from 'dotenv';
import path from 'path';

// Load environment variables from .env.local
dotenv.config({ path: path.join(process.cwd(), '.env.local') });

/**
 * Environment configuration interface
 */
export interface AdvancedSearchConfig {
  qdrant: {
    url: string;
    apiKey: string;
    defaultTimeout: number;
  };
  voyage: {
    apiKey: string;
    model: string;
    defaultDimension: number;
    maxTokens: number;
  };
  falkor: {
    host: string;
    port: number;
    username: string;
    password: string;
  };
  search: {
    defaultLimit: number;
    maxLimit: number;
    defaultSnippetWords: number;
    cacheTimeout: number;
  };
  sync: {
    batchSize: number;
    maxRetries: number;
    retryDelay: number;
  };
}

/**
 * Default configuration values
 */
const DEFAULT_CONFIG = {
  qdrant: {
    defaultTimeout: 30000, // 30 seconds
  },
  voyage: {
    model: 'voyage-3.5',
    defaultDimension: 1024,
    maxTokens: 320000,
  },
  falkor: {
    port: 6379,
  },
  search: {
    defaultLimit: 10,
    maxLimit: 100,
    defaultSnippetWords: 10,
    cacheTimeout: 300000, // 5 minutes
  },
  sync: {
    batchSize: 1000,
    maxRetries: 3,
    retryDelay: 1000, // 1 second
  },
};

/**
 * Load and validate configuration from environment variables
 */
export function loadConfig(): AdvancedSearchConfig {
  const config: AdvancedSearchConfig = {
    qdrant: {
      url: process.env.QDRANT_URL || '',
      apiKey: process.env.QDRANT_API_KEY || '',
      defaultTimeout: parseInt(process.env.QDRANT_TIMEOUT || '30000'),
    },
    voyage: {
      apiKey: process.env.VOYAGE_API_KEY || '',
      model: process.env.VOYAGE_MODEL || DEFAULT_CONFIG.voyage!.model!,
      defaultDimension: parseInt(process.env.VOYAGE_DIMENSION || '1024'),
      maxTokens: parseInt(process.env.VOYAGE_MAX_TOKENS || '320000'),
    },
    falkor: {
      host: process.env.FALKOR_HOST || '',
      port: parseInt(process.env.FALKOR_PORT || '6379'),
      username: process.env.FALKOR_USERNAME || '',
      password: process.env.FALKOR_PASSWORD || '',
    },
    search: {
      defaultLimit: parseInt(process.env.SEARCH_DEFAULT_LIMIT || '10'),
      maxLimit: parseInt(process.env.SEARCH_MAX_LIMIT || '100'),
      defaultSnippetWords: parseInt(process.env.SEARCH_SNIPPET_WORDS || '10'),
      cacheTimeout: parseInt(process.env.SEARCH_CACHE_TIMEOUT || '300000'),
    },
    sync: {
      batchSize: parseInt(process.env.SYNC_BATCH_SIZE || '1000'),
      maxRetries: parseInt(process.env.SYNC_MAX_RETRIES || '3'),
      retryDelay: parseInt(process.env.SYNC_RETRY_DELAY || '1000'),
    },
  };

  return config;
}

/**
 * Validate required environment variables
 */
export function validateConfig(config: AdvancedSearchConfig): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Qdrant validation
  if (!config.qdrant.url) {
    errors.push('QDRANT_URL environment variable is required');
  } else {
    try {
      new URL(config.qdrant.url);
    } catch {
      errors.push('QDRANT_URL must be a valid URL');
    }
  }

  if (!config.qdrant.apiKey) {
    errors.push('QDRANT_API_KEY environment variable is required');
  }

  // Voyage AI validation
  if (!config.voyage.apiKey) {
    errors.push('VOYAGE_API_KEY environment variable is required');
  }

  if (config.voyage.defaultDimension <= 0 || config.voyage.defaultDimension > 4096) {
    errors.push('VOYAGE_DIMENSION must be between 1 and 4096');
  }

  // FalkorDB validation
  if (!config.falkor.host) {
    errors.push('FALKOR_HOST environment variable is required');
  }

  if (!config.falkor.username) {
    errors.push('FALKOR_USERNAME environment variable is required');
  }

  if (!config.falkor.password) {
    errors.push('FALKOR_PASSWORD environment variable is required');
  }

  // Search limits validation
  if (config.search.defaultLimit > config.search.maxLimit) {
    errors.push('SEARCH_DEFAULT_LIMIT cannot be greater than SEARCH_MAX_LIMIT');
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * Get configuration with validation
 */
export function getValidatedConfig(): AdvancedSearchConfig {
  const config = loadConfig();
  const validation = validateConfig(config);

  if (!validation.valid) {
    const errorMessage = `Configuration validation failed:\n${validation.errors.join('\n')}`;
    throw new Error(errorMessage);
  }

  return config;
}

/**
 * Environment variable documentation
 */
export const ENV_DOCS = {
  QDRANT_URL: 'Qdrant Cloud endpoint URL (e.g., https://your-cluster.qdrant.tech:6333)',
  QDRANT_API_KEY: 'Qdrant Cloud API key for authentication',
  QDRANT_TIMEOUT: 'Qdrant request timeout in milliseconds (default: 30000)',
  
  VOYAGE_API_KEY: 'Voyage AI API key for embeddings generation',
  VOYAGE_MODEL: 'Voyage AI model name (default: voyage-3.5)',
  VOYAGE_DIMENSION: 'Embedding dimension (default: 1024, supports 256, 512, 1024, 2048)',
  VOYAGE_MAX_TOKENS: 'Maximum tokens per batch request (default: 320000)',
  
  FALKOR_HOST: 'FalkorDB host address',
  FALKOR_PORT: 'FalkorDB port (default: 6379)',
  FALKOR_USERNAME: 'FalkorDB username for authentication',
  FALKOR_PASSWORD: 'FalkorDB password for authentication',
  
  SEARCH_DEFAULT_LIMIT: 'Default number of search results (default: 10)',
  SEARCH_MAX_LIMIT: 'Maximum allowed search results (default: 100)',
  SEARCH_SNIPPET_WORDS: 'Default context words for snippets (default: 10)',
  SEARCH_CACHE_TIMEOUT: 'Search result cache timeout in milliseconds (default: 300000)',
  
  SYNC_BATCH_SIZE: 'Batch size for data synchronization (default: 1000)',
  SYNC_MAX_RETRIES: 'Maximum retry attempts for failed operations (default: 3)',
  SYNC_RETRY_DELAY: 'Delay between retry attempts in milliseconds (default: 1000)',
};

/**
 * Print configuration status for debugging
 */
export function printConfigStatus(): void {
  try {
    const config = loadConfig();
    const validation = validateConfig(config);

    console.log('🔧 Advanced Search API Configuration Status');
    console.log('==========================================');
    
    console.log('\n📡 Qdrant Cloud:');
    console.log(`   URL: ${config.qdrant.url ? '✓ configured' : '❌ missing'}`);
    console.log(`   API Key: ${config.qdrant.apiKey ? '✓ configured' : '❌ missing'}`);
    console.log(`   Timeout: ${config.qdrant.defaultTimeout}ms`);
    
    console.log('\n🚀 Voyage AI:');
    console.log(`   API Key: ${config.voyage.apiKey ? '✓ configured' : '❌ missing'}`);
    console.log(`   Model: ${config.voyage.model}`);
    console.log(`   Dimension: ${config.voyage.defaultDimension}`);
    
    console.log('\n📊 FalkorDB:');
    console.log(`   Host: ${config.falkor.host ? '✓ configured' : '❌ missing'}`);
    console.log(`   Port: ${config.falkor.port}`);
    console.log(`   Username: ${config.falkor.username ? '✓ configured' : '❌ missing'}`);
    console.log(`   Password: ${config.falkor.password ? '✓ configured' : '❌ missing'}`);
    
    console.log('\n🔍 Search Settings:');
    console.log(`   Default Limit: ${config.search.defaultLimit}`);
    console.log(`   Max Limit: ${config.search.maxLimit}`);
    console.log(`   Snippet Words: ${config.search.defaultSnippetWords}`);
    
    console.log('\n🔄 Sync Settings:');
    console.log(`   Batch Size: ${config.sync.batchSize}`);
    console.log(`   Max Retries: ${config.sync.maxRetries}`);
    console.log(`   Retry Delay: ${config.sync.retryDelay}ms`);
    
    console.log(`\n✅ Validation: ${validation.valid ? 'PASSED' : 'FAILED'}`);
    
    if (!validation.valid) {
      console.log('\n❌ Configuration Errors:');
      validation.errors.forEach(error => console.log(`   - ${error}`));
    }
    
  } catch (error) {
    console.error('❌ Configuration error:', error);
  }
} 