/**
 * Test Suite for Task 1.1: Qdrant Cloud Integration
 * 
 * This file tests the Qdrant Cloud integration implementation
 * and provides examples of how to use the advanced search API.
 */

import { 
  getQdrantClient, 
  createCollection, 
  getCollectionName, 
  getCollectionInfo, 
  listCollections, 
  deleteCollection, 
  healthCheck,
  getHRCollectionConfig
} from './qdrant-client';

import { loadConfig, validateConfig, printConfigStatus } from './config';

/**
 * Test configuration loading and validation
 */
async function testConfiguration() {
  console.log('\n🔧 Testing Configuration Management...');
  
  try {
    // Test configuration loading
    console.log('📋 Loading configuration...');
    const config = loadConfig();
    
    // Test configuration validation
    console.log('✅ Validating configuration...');
    const validation = validateConfig(config);
    
    if (validation.valid) {
      console.log('✅ Configuration is valid');
    } else {
      console.log('❌ Configuration validation failed:');
      validation.errors.forEach(error => console.log(`   - ${error}`));
    }
    
    // Print configuration status
    printConfigStatus();
    
    return validation.valid;
    
  } catch (error) {
    console.error('❌ Configuration test failed:', error);
    return false;
  }
}

/**
 * Test collection naming function
 */
function testCollectionNaming() {
  console.log('\n📊 Testing Collection Naming...');
  
  const testCases = [
    { input: 'mock_hr', expected: 'mock_hr' },
    { input: 'knowledge_graph', expected: 'knowledge_graph' },
    { input: 'Test Graph!', expected: 'test_graph_' },
    { input: 'my-special-graph', expected: 'my-special-graph' },
    { input: 'UPPERCASE_GRAPH', expected: 'uppercase_graph' },
  ];
  
  let passed = 0;
  let failed = 0;
  
  testCases.forEach(({ input, expected }) => {
    try {
      const result = getCollectionName(input);
      if (result === expected) {
        console.log(`✅ "${input}" → "${result}"`);
        passed++;
      } else {
        console.log(`❌ "${input}" → "${result}" (expected: "${expected}")`);
        failed++;
      }
    } catch (error) {
      console.log(`❌ "${input}" → Error: ${error}`);
      failed++;
    }
  });
  
  console.log(`📊 Collection naming tests: ${passed} passed, ${failed} failed`);
  return failed === 0;
}

/**
 * Test collection configuration
 */
function testCollectionConfig() {
  console.log('\n⚙️ Testing Collection Configuration...');
  
  try {
    const config = getHRCollectionConfig();
    
    // Validate configuration structure
    const expectedKeys = ['vectors', 'optimizers_config', 'hnsw_config'];
    const hasAllKeys = expectedKeys.every(key => config.hasOwnProperty(key));
    
    if (!hasAllKeys) {
      console.log('❌ Missing required configuration keys');
      return false;
    }
    
    // Validate vector configuration
    if (config.vectors.size !== 1024) {
      console.log('❌ Incorrect vector size');
      return false;
    }
    
    if (config.vectors.distance !== 'Cosine') {
      console.log('❌ Incorrect distance metric');
      return false;
    }
    
    console.log('✅ Collection configuration is valid');
    console.log(`   - Vector size: ${config.vectors.size}`);
    console.log(`   - Distance metric: ${config.vectors.distance}`);
    console.log(`   - HNSW M parameter: ${config.hnsw_config.m}`);
    console.log(`   - HNSW ef_construct: ${config.hnsw_config.ef_construct}`);
    
    return true;
    
  } catch (error) {
    console.error('❌ Collection config test failed:', error);
    return false;
  }
}

/**
 * Test Qdrant client operations (with mock)
 */
async function testQdrantOperations() {
  console.log('\n🌐 Testing Qdrant Client Operations...');
  
  try {
    // Test health check
    console.log('🏥 Testing health check...');
    const health = await healthCheck();
    console.log(`   Status: ${health.status}`);
    console.log(`   Message: ${health.message}`);
    
    // Test client connection
    console.log('🔌 Testing client connection...');
    const client = await getQdrantClient();
    console.log('✅ Client connection established');
    
    // Test collection operations
    const testGraphName = 'mock_hr';
    
    console.log('📊 Testing collection creation...');
    const created = await createCollection(testGraphName);
    console.log(`   Creation result: ${created}`);
    
    console.log('📋 Testing collection listing...');
    const collections = await listCollections();
    console.log(`   Found ${collections.length} collections`);
    
    console.log('ℹ️ Testing collection info...');
    const info = await getCollectionInfo(testGraphName);
    if (info) {
      console.log(`   Collection: ${info.name}`);
      console.log(`   Graph: ${info.graphName}`);
      console.log(`   Vectors: ${info.vectorsCount}`);
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ Qdrant operations test failed:', error);
    return false;
  }
}

/**
 * Run all tests for Task 1.1
 */
async function runTask1Tests() {
  console.log('🚀 Running Task 1.1: Qdrant Cloud Integration Tests');
  console.log('====================================================');
  
  const results = {
    configuration: false,
    collectionNaming: false,
    collectionConfig: false,
    qdrantOperations: false,
  };
  
  // Run tests
  results.configuration = await testConfiguration();
  results.collectionNaming = testCollectionNaming();
  results.collectionConfig = testCollectionConfig();
  results.qdrantOperations = await testQdrantOperations();
  
  // Calculate overall result
  const passed = Object.values(results).filter(Boolean).length;
  const total = Object.keys(results).length;
  
  console.log('\n📊 Test Results Summary');
  console.log('========================');
  console.log(`Configuration: ${results.configuration ? '✅' : '❌'}`);
  console.log(`Collection Naming: ${results.collectionNaming ? '✅' : '❌'}`);
  console.log(`Collection Config: ${results.collectionConfig ? '✅' : '❌'}`);
  console.log(`Qdrant Operations: ${results.qdrantOperations ? '✅' : '❌'}`);
  console.log(`\nOverall: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('\n🎉 All Task 1.1 tests passed!');
    console.log('\n📋 Next Steps:');
    console.log('   1. Install required packages (see package-requirements.md)');
    console.log('   2. Set up Qdrant Cloud account and get credentials');
    console.log('   3. Update environment variables');
    console.log('   4. Replace mock implementation with real Qdrant client');
    console.log('   5. Proceed to Task 1.2: Data Migration Pipeline');
  } else {
    console.log('\n⚠️ Some tests failed. Please check the configuration and implementation.');
  }
  
  return passed === total;
}

/**
 * Example usage of the Qdrant client
 */
async function exampleUsage() {
  console.log('\n💡 Example Usage of Qdrant Client');
  console.log('==================================');
  
  try {
    // Example 1: Create collection for HR graph
    console.log('\n📊 Example 1: Create collection for HR graph');
    const hrGraphName = 'mock_hr';
    const collectionName = getCollectionName(hrGraphName);
    console.log(`   Graph: ${hrGraphName}`);
    console.log(`   Collection: ${collectionName}`);
    
    await createCollection(hrGraphName);
    console.log('   ✅ Collection created/verified');
    
    // Example 2: Get collection information
    console.log('\n📋 Example 2: Get collection information');
    const info = await getCollectionInfo(hrGraphName);
    if (info) {
      console.log(`   Name: ${info.name}`);
      console.log(`   Vectors: ${info.vectorsCount}`);
      console.log(`   Status: ${info.status}`);
    }
    
    // Example 3: List all collections
    console.log('\n📝 Example 3: List all collections');
    const collections = await listCollections();
    console.log(`   Total collections: ${collections.length}`);
    collections.forEach(name => console.log(`   - ${name}`));
    
    // Example 4: Health check
    console.log('\n🏥 Example 4: Health check');
    const health = await healthCheck();
    console.log(`   Status: ${health.status}`);
    console.log(`   Message: ${health.message}`);
    
  } catch (error) {
    console.error('❌ Example usage failed:', error);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTask1Tests()
    .then(success => {
      if (success) {
        console.log('\n🔍 Running example usage...');
        return exampleUsage();
      }
    })
    .catch(error => {
      console.error('❌ Test execution failed:', error);
      process.exit(1);
    });
}

// Export functions for use in other modules
export {
  testConfiguration,
  testCollectionNaming,
  testCollectionConfig,
  testQdrantOperations,
  runTask1Tests,
  exampleUsage,
}; 