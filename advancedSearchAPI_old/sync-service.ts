/**
 * Advanced Search API - Sync Service
 * 
 * Task 1.2: Data Migration Pipeline
 * This module handles syncing FalkorDB data to Qdrant Cloud with pagination support
 */

import { VoyageAIClient } from 'voyageai';
import { QdrantClient } from '@qdrant/js-client-rest';
import { getQdrantClient, createCollection, getCollectionName } from './qdrant-client';
import { getValidatedConfig } from './config';
import { spladeService, type SpladeVector } from './splade-service';
import type { HRSearchRecord, SyncStatus, BatchOperationResult } from './types';

// Configuration constants
const DEFAULT_BATCH_SIZE = 500;
const DEFAULT_API_BASE_URL = 'http://localhost:3000';
const VOYAGE_MODEL = 'voyage-3.5';
const EMBEDDING_DIMENSION = 1024;

/**
 * Sync FalkorDB data to Qdrant via API calls with pagination support
 */
export async function syncFalkorToQdrant(
  graphName: string = 'mock_hr',
  options: {
    apiBaseUrl?: string;
    batchSize?: number;
    skipExisting?: boolean;
    forceRecreate?: boolean;
  } = {}
): Promise<SyncStatus> {
  const startTime = Date.now();
  const config = await getValidatedConfig();
  
  const {
    apiBaseUrl = DEFAULT_API_BASE_URL,
    batchSize = DEFAULT_BATCH_SIZE,
    skipExisting = false,
    forceRecreate = false,
  } = options;

  console.log(`🔄 Starting sync: FalkorDB (${graphName}) → Qdrant`);
  console.log(`   API: ${apiBaseUrl}`);
  console.log(`   Batch size: ${batchSize}`);
  console.log(`   Skip existing: ${skipExisting}`);
  console.log(`   Force recreate: ${forceRecreate}`);

  try {
    // Initialize clients
    const qdrantClient = await getQdrantClient();
    const voyageClient = new VoyageAIClient({ apiKey: config.voyage.apiKey });
    const collectionName = getCollectionName(graphName);

    // Create or recreate collection
    await createCollection(collectionName, forceRecreate);
    
    // Check if collection already has data and skip if requested
    if (skipExisting && !forceRecreate) {
      try {
        const collectionInfo = await qdrantClient.getCollection(collectionName);
        if (collectionInfo && collectionInfo.points_count && collectionInfo.points_count > 0) {
          console.log(`⏭️  Collection '${collectionName}' already has ${collectionInfo.points_count} points, skipping sync`);
          return {
            success: true,
            graphName,
            collectionName,
            totalProcessed: 0,
            totalSynced: 0,
            errors: [],
            duration: Date.now() - startTime,
            message: 'Skipped - collection already exists with data'
          };
        }
      } catch (error) {
        console.log(`📝 Collection info check failed, proceeding with sync: ${error}`);
      }
    }

    // Fetch all data from FalkorDB with pagination
    const allNodes: any[] = [];
    const allRecords: any[] = [];
    let page = 0;
    let hasMore = true;
    let totalFetched = 0;

    console.log(`📥 Fetching data from FalkorDB API with pagination...`);

    while (hasMore) {
      try {
        const response = await fetch(
          `${apiBaseUrl}/api/data-falkor?graph=${encodeURIComponent(graphName)}&page=${page}&limit=${batchSize}`
        );

        if (!response.ok) {
          throw new Error(`API request failed: ${response.status} ${response.statusText}`);
        }

        const falkorData = await response.json();

        if (!falkorData.success) {
          throw new Error(`FalkorDB API error: ${falkorData.error}`);
        }

        const pageNodes = falkorData.data.nodes || [];
        const pageRecords = falkorData.data.records || [];

        allNodes.push(...pageNodes);
        allRecords.push(...pageRecords);

        totalFetched += pageNodes.length + pageRecords.length;

        console.log(`   Page ${page}: ${pageNodes.length} nodes, ${pageRecords.length} records`);

        // Check if we have more data
        hasMore = falkorData.meta?.pagination?.has_more || false;
        
        if (!hasMore && (pageNodes.length === 0 && pageRecords.length === 0)) {
          break;
        }

        page++;
      } catch (error) {
        console.error(`❌ Error fetching page ${page}:`, error);
        throw new Error(`Failed to fetch data from FalkorDB API: ${error}`);
      }
    }

    console.log(`✅ Fetched total: ${allNodes.length} nodes and ${allRecords.length} records`);

    // Transform data to search-optimized format
    const searchRecords = transformToSearchRecords(allNodes, allRecords);
    console.log(`🔄 Transformed to ${searchRecords.length} search records`);

    // Generate embeddings and sync to Qdrant in batches
    let totalSynced = 0;
    const errors: string[] = [];

    for (let i = 0; i < searchRecords.length; i += batchSize) {
      const batch = searchRecords.slice(i, i + batchSize);
      const batchNumber = Math.floor(i / batchSize) + 1;
      const totalBatches = Math.ceil(searchRecords.length / batchSize);

      console.log(`🚀 Processing batch ${batchNumber}/${totalBatches} (${batch.length} records)`);

      try {
        const result = await processBatch(batch, qdrantClient, voyageClient, collectionName);
        totalSynced += result.synced;
        if (result.errors.length > 0) {
          errors.push(...result.errors);
        }
      } catch (error) {
        const errorMsg = `Batch ${batchNumber} failed: ${error}`;
        console.error(`❌ ${errorMsg}`);
        errors.push(errorMsg);
      }
    }

    const duration = Date.now() - startTime;
    const syncStatus: SyncStatus = {
      success: errors.length === 0,
      graphName,
      collectionName,
      totalProcessed: searchRecords.length,
      totalSynced,
      errors,
      duration,
      message: errors.length === 0 
        ? `Successfully synced ${totalSynced}/${searchRecords.length} records`
        : `Synced ${totalSynced}/${searchRecords.length} records with ${errors.length} errors`
    };

    console.log(`✨ Sync completed in ${duration}ms`);
    console.log(`   Success: ${syncStatus.success}`);
    console.log(`   Processed: ${syncStatus.totalProcessed}`);
    console.log(`   Synced: ${syncStatus.totalSynced}`);
    console.log(`   Errors: ${syncStatus.errors.length}`);

    return syncStatus;

  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMsg = `Sync failed: ${error}`;
    console.error(`❌ ${errorMsg}`);

    return {
      success: false,
      graphName,
      collectionName: getCollectionName(graphName),
      totalProcessed: 0,
      totalSynced: 0,
      errors: [errorMsg],
      duration,
      message: errorMsg
    };
  }
}

/**
 * Process a batch of records: generate embeddings and store in Qdrant
 */
async function processBatch(
  batch: HRSearchRecord[],
  qdrantClient: QdrantClient,
  voyageClient: VoyageAIClient,
  collectionName: string
): Promise<BatchOperationResult> {
  const errors: string[] = [];
  let synced = 0;

  try {
    // Generate combined text for embedding
    const textsForEmbedding = batch.map(record => {
      const title = record.title || '';
      const content = record.content || '';
      const summary = record.summary || '';
      const fullContent = record.fullContent || '';
      return `${title} ${content} ${summary} ${fullContent}`.trim();
    });

    // Generate embeddings using Voyage AI
    console.log(`   🔮 Generating dense embeddings for ${batch.length} records...`);
    const embeddingResponse = await voyageClient.embed({
      input: textsForEmbedding,
      model: VOYAGE_MODEL,
      inputType: 'document'
    });

    if (!embeddingResponse.data || embeddingResponse.data.length !== batch.length) {
      throw new Error(`Embedding generation failed: expected ${batch.length} embeddings, got ${embeddingResponse.data?.length || 0}`);
    }

    // Generate sparse vectors using SPLADE
    console.log(`   🕸️  Generating sparse vectors for ${batch.length} records...`);
    const sparseVectors = await spladeService.generateSparseVectors(textsForEmbedding);

    if (sparseVectors.length !== batch.length) {
      console.warn(`⚠️  SPLADE generation mismatch: expected ${batch.length} vectors, got ${sparseVectors.length}`);
      // Fill missing vectors with empty sparse vectors
      while (sparseVectors.length < batch.length) {
        sparseVectors.push({ indices: [], values: [] });
      }
    }

    // Prepare points for Qdrant - generate deterministic UUID from original ID
    const { createHash } = await import('crypto');
    const points = batch.map((record, index) => {
      // Create deterministic UUID from original ID using MD5 hash
      const hash = createHash('md5').update(record.id).digest('hex');
      const deterministicUUID = `${hash.slice(0,8)}-${hash.slice(8,12)}-${hash.slice(12,16)}-${hash.slice(16,20)}-${hash.slice(20,32)}`;
      
      const embedding = embeddingResponse.data?.[index]?.embedding;
      if (!embedding) {
        throw new Error(`Missing embedding for record ${index}`);
      }
      
      const sparseVector = sparseVectors[index];
      
      return {
        id: deterministicUUID, // Deterministic UUID for Qdrant compatibility
        vector: {
          dense: embedding, // Named vector format for dense vectors
          sparse: sparseVector // Sparse vector as named vector in same field
        },
        payload: {
          ...record,
          // Keep original ID in payload for reference
          original_id: record.id,
          // Add metadata for search
          _sync_timestamp: new Date().toISOString(),
          _embedding_model: VOYAGE_MODEL,
          _embedding_dimension: EMBEDDING_DIMENSION,
          _splade_model: 'naver/splade-v3',
          _has_sparse_vector: sparseVector.indices.length > 0,
        }
      };
    });

    // Store in Qdrant
    console.log(`   💾 Storing ${points.length} points in Qdrant...`);
    const upsertResponse = await qdrantClient.upsert(collectionName, {
      wait: true,
      points: points
    });

    if (upsertResponse.status !== 'completed' && upsertResponse.status !== 'acknowledged') {
      throw new Error(`Qdrant upsert failed: ${upsertResponse.status}`);
    }

    synced = points.length;
    console.log(`   ✅ Successfully stored ${synced} points`);

  } catch (error) {
    const errorMsg = `Batch processing failed: ${error}`;
    console.error(`   ❌ ${errorMsg}`);
    errors.push(errorMsg);
  }

  return { synced, errors };
}

/**
 * Transform FalkorDB nodes and records to search-optimized format
 */
function transformToSearchRecords(nodes: any[], records: any[]): HRSearchRecord[] {
  const searchRecords: HRSearchRecord[] = [];

  // Process category nodes
  for (const node of nodes) {
    const record: HRSearchRecord = {
      id: `node-${node.id}`,
      title: node.path || node.id,
      content: node.summary || '',
      summary: node.refined_summary || node.summary || '',
      breadcrumb: node.path || '',
      section_path: node.path ? node.path.split(' > ') : [],
      article_type: 'section',
      level: node.level || 0,
      nodeType: 'category',
      country: node.country || 'general'
    };
    searchRecords.push(record);
  }

  // Process record nodes
  for (const record of records) {
    const fields = record.fields || {};
    const searchRecord: HRSearchRecord = {
      id: `record-${record.id}`,
      title: fields.Title || 'Untitled Document',
      content: fields['Cleaned Body'] || '',
      fullContent: fields['Cleaned Body'] || '',
      breadcrumb: fields.Breadcrumb || '',
      section_path: fields.Breadcrumb ? fields.Breadcrumb.split(' > ') : [],
      article_type: 'article',
      level: fields.Breadcrumb ? fields.Breadcrumb.split(' > ').length - 1 : 0,
      nodeType: 'record',
      country: fields.Country || 'general',
      htmlUrl: fields['HTML URL'] || '',
      sectionId: fields['Section ID'],
      recordId: fields.ID
    };
    searchRecords.push(searchRecord);
  }

  return searchRecords;
}

/**
 * Check sync status for a collection
 */
export async function getSyncStatus(graphName: string = 'mock_hr'): Promise<{
  collectionExists: boolean;
  pointCount: number;
  lastSync?: string;
  collectionInfo?: any;
}> {
  try {
    const qdrantClient = await getQdrantClient();
    const collectionName = getCollectionName(graphName);

    const collectionInfo = await qdrantClient.getCollection(collectionName);
    
    if (collectionInfo && collectionInfo.points_count !== undefined) {
      return {
        collectionExists: true,
        pointCount: collectionInfo.points_count || 0,
        collectionInfo: collectionInfo
      };
    } else {
      return {
        collectionExists: false,
        pointCount: 0
      };
    }
  } catch (error) {
    console.error('Error checking sync status:', error);
    return {
      collectionExists: false,
      pointCount: 0
    };
  }
}

/**
 * Direct data sync from source files (for initial setup or testing)
 */
export async function syncDirectData(
  graphName: string = 'mock_hr',
  sourceData: any,
  options: {
    forceRecreate?: boolean;
  } = {}
): Promise<SyncStatus> {
  const startTime = Date.now();
  const config = await getValidatedConfig();
  
  console.log(`🔄 Starting direct sync for graph: ${graphName}`);

  try {
    const qdrantClient = await getQdrantClient();
    const voyageClient = new VoyageAIClient({ apiKey: config.voyage.apiKey });
    const collectionName = getCollectionName(graphName);

    // Create collection
    await createCollection(collectionName, options.forceRecreate || false);

    // Transform source data
    const searchRecords = Array.isArray(sourceData) 
      ? transformDirectData(sourceData)
      : transformToSearchRecords(sourceData.nodes || [], sourceData.records || []);

    console.log(`🔄 Processing ${searchRecords.length} records...`);

    // Process in single batch (for direct sync, usually smaller datasets)
    const result = await processBatch(searchRecords, qdrantClient, voyageClient, collectionName);

    const duration = Date.now() - startTime;
    const syncStatus: SyncStatus = {
      success: result.errors.length === 0,
      graphName,
      collectionName,
      totalProcessed: searchRecords.length,
      totalSynced: result.synced,
      errors: result.errors,
      duration,
      message: result.errors.length === 0 
        ? `Successfully synced ${result.synced} records`
        : `Sync completed with ${result.errors.length} errors`
    };

    console.log(`✨ Direct sync completed in ${duration}ms`);
    return syncStatus;

  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMsg = `Direct sync failed: ${error}`;
    console.error(`❌ ${errorMsg}`);

    return {
      success: false,
      graphName,
      collectionName: getCollectionName(graphName),
      totalProcessed: 0,
      totalSynced: 0,
      errors: [errorMsg],
      duration,
      message: errorMsg
    };
  }
}

/**
 * Transform direct data (e.g., from JSON files) to search records
 */
function transformDirectData(data: any[]): HRSearchRecord[] {
  // This would implement transformation logic for direct file imports
  // Implementation depends on source data format
  const searchRecords: HRSearchRecord[] = [];
  
  // Example transformation for HR data structure
  for (const item of data) {
    if (item.title) {
      const record: HRSearchRecord = {
        id: `direct-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        title: item.title,
        content: item.content || '',
        summary: item.summary || '',
        fullContent: item.fullContent || item.content || '',
        breadcrumb: item.breadcrumb || item.title,
        section_path: item.section_path || [item.title],
        article_type: item.article_type || 'article',
        level: item.level || 0,
        nodeType: item.nodeType || 'record',
        country: item.country || 'general'
      };
      searchRecords.push(record);
    }
  }
  
  return searchRecords;
}

/**
 * Export main sync function for convenience
 */
export { syncFalkorToQdrant as sync }; 