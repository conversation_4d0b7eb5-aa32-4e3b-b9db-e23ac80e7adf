# Advanced Search API - Package Requirements

## Task 1.1: Qdrant Cloud Integration - Required Packages

### Required NPM Packages

Install the following packages to complete the Qdrant Cloud integration:

```bash
# Qdrant client for Node.js
pnpm add qdrant-client

# Voyage AI client for embeddings
pnpm add voyageai

# HTTP client for API calls
pnpm add httpx

# Environment variable management
pnpm add dotenv

# Additional utility packages
pnpm add @types/node
```

### Package Details

#### 1. Qdrant Client

```bash
pnpm add qdrant-client
```

- **Purpose**: Official Qdrant client for Node.js/TypeScript
- **Usage**: Cloud connection, collection management, vector operations
- **Documentation**: https://qdrant.tech/documentation/frameworks/js/

#### 2. Voyage AI Client

```bash
pnpm add voyageai
```

- **Purpose**: Generate embeddings using Voyage AI models
- **Usage**: Text to vector conversion for semantic search
- **Documentation**: https://docs.voyageai.com/

#### 3. HTTP Client

```bash
pnpm add httpx
```

- **Purpose**: Making HTTP requests for FalkorDB API integration
- **Usage**: Fetching data from FalkorDB with pagination
- **Alternative**: Can use built-in `fetch` or `axios`

### Environment Variables Required

Create or update your `.env.local` file with the following variables:

```bash
# Qdrant Cloud Configuration
QDRANT_URL=https://your-cluster-url.qdrant.tech:6333
QDRANT_API_KEY=your-qdrant-cloud-api-key

# Voyage AI Configuration
VOYAGE_API_KEY=your-voyage-api-key

# FalkorDB Configuration (already exists)
FALKOR_HOST=your-falkor-host
FALKOR_USERNAME=your-falkor-username
FALKOR_PASSWORD=your-falkor-password
FALKOR_PORT=6379

# Optional Configuration
SEARCH_DEFAULT_LIMIT=10
SEARCH_MAX_LIMIT=100
SYNC_BATCH_SIZE=1000
```

### Installation Commands

Run these commands in your project root:

```bash
# Install all required packages (using pnpm)
pnpm add qdrant-client voyageai httpx dotenv @types/node

# Alternative (not recommended - use pnpm):
# npm install qdrant-client voyageai httpx dotenv @types/node
# yarn add qdrant-client voyageai httpx dotenv @types/node
```

### After Installation

1. **Update imports** in `qdrant-client.ts`:

   ```typescript
   // Replace mock imports with real ones
   import { QdrantClient } from "qdrant-client";
   import { Schemas } from "qdrant-client";
   ```

2. **Remove mock client** and use real QdrantClient:

   ```typescript
   // Replace createMockClient() with real client initialization
   qdrantClient = new QdrantClient({
     url: config.url,
     apiKey: config.apiKey,
   });
   ```

3. **Test the connection**:

   ```bash
   # Run the configuration test
   pnpm test:config

   # Or test manually
   node -e "
   require('dotenv').config();
   const { printConfigStatus } = require('./advancedSearchAPI/config.ts');
   printConfigStatus();
   "
   ```

### Qdrant Cloud Setup

1. **Create Qdrant Cloud Account**:

   - Visit: https://cloud.qdrant.io/
   - Sign up for a free account
   - Create a new cluster

2. **Get Connection Details**:

   - Cluster URL (e.g., `https://xyz-abc.qdrant.tech:6333`)
   - API Key from the cluster settings

3. **Test Connection**:

   ```typescript
   import { healthCheck } from "./advancedSearchAPI/qdrant-client";

   healthCheck().then((result) => {
     console.log("Qdrant Status:", result);
   });
   ```

### Voyage AI Setup

1. **Create Voyage AI Account**:

   - Visit: https://www.voyageai.com/
   - Sign up and get API key

2. **Test Embedding Generation**:

   ```typescript
   import voyageai from "voyageai";

   const vo = voyageai.Client(process.env.VOYAGE_API_KEY);
   const result = await vo.embed({
     texts: ["test embedding"],
     model: "voyage-3.5",
     input_type: "document",
   });
   console.log("Embedding dimension:", result.embeddings[0].length);
   ```

### Current Implementation Status

✅ **Completed**:

- Configuration management (`config.ts`)
- Type definitions (`types.ts`)
- Qdrant client interface (`qdrant-client.ts`)
- Mock implementation for development

🔄 **Pending Package Installation**:

- `qdrant-client` - Replace mock client
- `voyageai` - Add embedding generation
- `httpx` - FalkorDB API integration

⚠️ **Next Steps**:

1. Install required packages
2. Replace mock implementations
3. Test cloud connections
4. Implement Task 1.2 (Data Migration Pipeline)

### Troubleshooting

#### Common Issues:

1. **Package conflicts**: Use exact versions if needed
2. **TypeScript errors**: Ensure `@types/node` is installed
3. **Environment variables**: Double-check `.env.local` file
4. **Network issues**: Verify Qdrant Cloud cluster is running

#### Support:

- Qdrant: https://qdrant.tech/documentation/
- Voyage AI: https://docs.voyageai.com/
- Project issues: Check implementation guide
