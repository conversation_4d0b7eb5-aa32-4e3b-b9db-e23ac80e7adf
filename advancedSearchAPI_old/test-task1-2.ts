/**
 * Advanced Search API - Task 1.2 Tests
 * 
 * Data Migration Pipeline Testing
 * Tests for FalkorDB → Qdrant sync with pagination support
 */

import { syncFalkorToQdrant, getSyncStatus, syncDirectData } from './sync-service';
import { getValidatedConfig, printConfigStatus } from './config';
import { getQdrantClient, listCollections, deleteCollection } from './qdrant-client';
import type { SyncStatus } from './types';

/**
 * Test pagination API functionality
 */
async function testPaginationAPI(
  graphName: string = 'mock_hr',
  apiBaseUrl: string = 'http://localhost:3000'
): Promise<void> {
  console.log('\n📊 Testing Pagination API');
  console.log('========================');

  try {
    // Test basic pagination
    console.log('🔍 Testing basic pagination...');
    
    const response1 = await fetch(
      `${apiBaseUrl}/api/data-falkor?graph=${graphName}&page=0&limit=10`
    );
    
    if (!response1.ok) {
      throw new Error(`API request failed: ${response1.status} ${response1.statusText}`);
    }
    
    const data1 = await response1.json();
    
    if (!data1.success) {
      throw new Error(`API error: ${data1.error}`);
    }
    
    console.log(`✅ Page 0: ${data1.data.nodes.length} nodes, ${data1.data.records.length} records`);
    console.log(`📄 Pagination: page=${data1.meta.pagination.page}, limit=${data1.meta.pagination.limit}`);
    console.log(`📊 Totals: ${data1.meta.pagination.total_categories} categories, ${data1.meta.pagination.total_records} records`);
    console.log(`🔄 Has more: ${data1.meta.pagination.has_more}`);
    
    // Test second page if available
    if (data1.meta.pagination.has_more) {
      console.log('\n🔍 Testing second page...');
      
      const response2 = await fetch(
        `${apiBaseUrl}/api/data-falkor?graph=${graphName}&page=1&limit=10`
      );
      
      const data2 = await response2.json();
      
      if (data2.success) {
        console.log(`✅ Page 1: ${data2.data.nodes.length} nodes, ${data2.data.records.length} records`);
        console.log(`🔄 Has more: ${data2.meta.pagination.has_more}`);
      }
    }
    
    // Test parameter validation
    console.log('\n🔍 Testing parameter validation...');
    
    const invalidResponse = await fetch(
      `${apiBaseUrl}/api/data-falkor?graph=${graphName}&page=-1&limit=0`
    );
    
    const invalidData = await invalidResponse.json();
    
    if (!invalidData.success && invalidData.error) {
      console.log(`✅ Parameter validation working: ${invalidData.error}`);
    } else {
      console.log(`⚠️ Parameter validation may not be working properly`);
    }
    
  } catch (error) {
    console.error(`❌ Pagination API test failed: ${error}`);
    throw error;
  }
}

/**
 * Test sync service functionality
 */
async function testSyncService(
  graphName: string = 'mock_hr',
  options: {
    apiBaseUrl?: string;
    batchSize?: number;
    forceRecreate?: boolean;
  } = {}
): Promise<SyncStatus> {
  console.log('\n🔄 Testing Sync Service');
  console.log('=======================');

  const {
    apiBaseUrl = 'http://localhost:3000',
    batchSize = 100,
    forceRecreate = true
  } = options;

  try {
    // Check initial sync status
    console.log('📊 Checking initial sync status...');
    const initialStatus = await getSyncStatus(graphName);
    console.log(`   Collection exists: ${initialStatus.collectionExists}`);
    console.log(`   Point count: ${initialStatus.pointCount}`);

    // Perform sync
    console.log('\n🚀 Starting sync operation...');
    const syncResult = await syncFalkorToQdrant(graphName, {
      apiBaseUrl,
      batchSize,
      forceRecreate
    });

    console.log('\n📊 Sync Results:');
    console.log(`   Success: ${syncResult.success}`);
    console.log(`   Graph: ${syncResult.graphName}`);
    console.log(`   Collection: ${syncResult.collectionName}`);
    console.log(`   Processed: ${syncResult.totalProcessed}`);
    console.log(`   Synced: ${syncResult.totalSynced}`);
    console.log(`   Duration: ${syncResult.duration}ms`);
    console.log(`   Message: ${syncResult.message}`);

    if (syncResult.errors.length > 0) {
      console.log(`   Errors: ${syncResult.errors.length}`);
      syncResult.errors.forEach((error, index) => {
        console.log(`     ${index + 1}. ${error}`);
      });
    }

    // Check final sync status
    console.log('\n📊 Checking final sync status...');
    const finalStatus = await getSyncStatus(graphName);
    console.log(`   Collection exists: ${finalStatus.collectionExists}`);
    console.log(`   Point count: ${finalStatus.pointCount}`);

    return syncResult;

  } catch (error) {
    console.error(`❌ Sync service test failed: ${error}`);
    throw error;
  }
}

/**
 * Test direct data sync functionality
 */
async function testDirectSync(): Promise<void> {
  console.log('\n📁 Testing Direct Data Sync');
  console.log('============================');

  const testGraphName = 'test_direct_sync';
  
  try {
    // Sample test data
    const testData = [
      {
        title: 'Test Article 1',
        content: 'This is test content for article 1',
        summary: 'Test summary 1',
        section_path: ['Test Section'],
        article_type: 'article',
        level: 1,
        nodeType: 'record',
        country: 'general'
      },
      {
        title: 'Test Article 2',
        content: 'This is test content for article 2',
        summary: 'Test summary 2',
        section_path: ['Test Section', 'Subsection'],
        article_type: 'article',
        level: 2,
        nodeType: 'record',
        country: 'general'
      }
    ];

    console.log(`🔄 Syncing ${testData.length} test records...`);
    
    const syncResult = await syncDirectData(testGraphName, testData, {
      forceRecreate: true
    });

    console.log('\n📊 Direct Sync Results:');
    console.log(`   Success: ${syncResult.success}`);
    console.log(`   Processed: ${syncResult.totalProcessed}`);
    console.log(`   Synced: ${syncResult.totalSynced}`);
    console.log(`   Duration: ${syncResult.duration}ms`);

    // Clean up test collection
    try {
      await deleteCollection(syncResult.collectionName);
      console.log(`🧹 Cleaned up test collection: ${syncResult.collectionName}`);
    } catch (cleanupError) {
      console.log(`⚠️ Cleanup warning: ${cleanupError}`);
    }

  } catch (error) {
    console.error(`❌ Direct sync test failed: ${error}`);
    throw error;
  }
}

/**
 * Test error handling and edge cases
 */
async function testErrorHandling(): Promise<void> {
  console.log('\n🛡️ Testing Error Handling');
  console.log('==========================');

  try {
    // Test with non-existent graph
    console.log('🔍 Testing non-existent graph...');
    
    try {
      await syncFalkorToQdrant('non_existent_graph_12345', {
        apiBaseUrl: 'http://localhost:3000',
        batchSize: 10
      });
      console.log('⚠️ Expected error but sync succeeded');
    } catch (error) {
      console.log(`✅ Correctly handled non-existent graph: ${error}`);
    }

    // Test with invalid API URL
    console.log('\n🔍 Testing invalid API URL...');
    
    try {
      await syncFalkorToQdrant('mock_hr', {
        apiBaseUrl: 'http://invalid-url-that-does-not-exist.com',
        batchSize: 10
      });
      console.log('⚠️ Expected error but sync succeeded');
    } catch (error) {
      console.log(`✅ Correctly handled invalid API URL: ${error}`);
    }

    console.log('\n✅ Error handling tests completed');

  } catch (error) {
    console.error(`❌ Error handling test failed: ${error}`);
    throw error;
  }
}

/**
 * Performance test with different batch sizes
 */
async function testBatchSizes(
  graphName: string = 'mock_hr',
  batchSizes: number[] = [50, 100, 200, 500]
): Promise<void> {
  console.log('\n⚡ Testing Batch Size Performance');
  console.log('=================================');

  const results: Array<{
    batchSize: number;
    duration: number;
    processed: number;
    synced: number;
    success: boolean;
  }> = [];

  for (const batchSize of batchSizes) {
    console.log(`\n🔄 Testing batch size: ${batchSize}`);
    
    try {
      const testGraphName = `${graphName}_batch_${batchSize}`;
      const syncResult = await syncFalkorToQdrant(testGraphName, {
        batchSize,
        forceRecreate: true
      });

      results.push({
        batchSize,
        duration: syncResult.duration,
        processed: syncResult.totalProcessed,
        synced: syncResult.totalSynced,
        success: syncResult.success
      });

      console.log(`   Duration: ${syncResult.duration}ms`);
      console.log(`   Processed: ${syncResult.totalProcessed}`);
      console.log(`   Synced: ${syncResult.totalSynced}`);

      // Clean up test collection
      try {
        await deleteCollection(syncResult.collectionName);
      } catch (cleanupError) {
        console.log(`⚠️ Cleanup warning: ${cleanupError}`);
      }

    } catch (error) {
      console.error(`❌ Batch size ${batchSize} failed: ${error}`);
      results.push({
        batchSize,
        duration: 0,
        processed: 0,
        synced: 0,
        success: false
      });
    }
  }

  // Report results
  console.log('\n📊 Batch Size Performance Results:');
  console.log('===================================');
  
  for (const result of results) {
    const rate = result.duration > 0 ? (result.processed / result.duration * 1000).toFixed(2) : '0';
    console.log(`   Batch ${result.batchSize}: ${result.duration}ms (${rate} records/sec) - ${result.success ? '✅' : '❌'}`);
  }
}

/**
 * Run comprehensive Task 1.2 tests
 */
async function runTask1_2Tests(): Promise<void> {
  console.log('🧪 Advanced Search API - Task 1.2 Tests');
  console.log('=========================================');

  try {
    // Check configuration
    console.log('\n🔧 Checking configuration...');
    await printConfigStatus();

    // Test pagination API
    await testPaginationAPI();

    // Test sync service
    await testSyncService();

    // Test direct sync
    await testDirectSync();

    // Test error handling
    await testErrorHandling();

    // Performance tests (optional, can be time-consuming)
    console.log('\n⚡ Running performance tests...');
    console.log('(This may take a few minutes)');
    await testBatchSizes('mock_hr', [100, 200]);

    console.log('\n🎉 All Task 1.2 tests completed successfully!');
    console.log('\n📋 Task 1.2: Data Migration Pipeline - ✅ COMPLETED');
    console.log('\n🚀 Ready for Task 2.1: Exact Matching Implementation');

  } catch (error) {
    console.error('\n❌ Task 1.2 tests failed:', error);
    throw error;
  }
}

// Main execution
if (require.main === module) {
  runTask1_2Tests()
    .then(() => {
      console.log('\n✨ Task 1.2 testing completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Task 1.2 testing failed:', error);
      process.exit(1);
    });
}

export {
  testPaginationAPI,
  testSyncService,
  testDirectSync,
  testErrorHandling,
  testBatchSizes,
  runTask1_2Tests
}; 