/**
 * Qdrant Cloud Client Configuration
 * 
 * This module provides the main interface for connecting to Qdrant Cloud
 * and managing collections for the advanced search API.
 * 
 * Task 1.1: Qdrant Cloud Integration
 * - Cloud endpoint and API key configuration
 * - Dynamic collection naming based on FalkorDB graph names
 * - Collection schema for HR records with 1024-dimensional vectors
 * - Cosine similarity distance metric
 * 
 * Qdrant client package installed and ready to use
 */

import { QdrantClient } from '@qdrant/js-client-rest';

// Local type definitions
interface QdrantConfig {
  url: string;
  apiKey: string;
}

interface CollectionInfo {
  name: string;
  graphName: string;
  vectorsCount: number;
  status: string;
  vectorSize: number;
  distance: string;
}

class QdrantConnectionError extends Error {
  public readonly originalError?: Error;

  constructor(message: string, originalError?: Error) {
    super(message);
    this.name = 'QdrantConnectionError';
    this.originalError = originalError;
  }
}

/**
 * Qdrant Cloud client singleton
 */
let qdrantClient: QdrantClient | null = null;

/**
 * Get Qdrant Cloud configuration from environment variables
 */
function getQdrantConfig(): QdrantConfig {
  const config: QdrantConfig = {
    url: process.env.QDRANT_URL || '',
    apiKey: process.env.QDRANT_API_KEY || '',
  };

  // Validate required configuration
  if (!config.url) {
    throw new Error('QDRANT_URL environment variable is required');
  }
  
  if (!config.apiKey) {
    throw new Error('QDRANT_API_KEY environment variable is required');
  }

  // Validate URL format
  try {
    new URL(config.url);
  } catch {
    throw new Error('QDRANT_URL must be a valid URL');
  }

  return config;
}

/**
 * Get or create Qdrant Cloud connection
 */
export async function getQdrantClient(): Promise<QdrantClient> {
  if (!qdrantClient) {
    try {
      const config = getQdrantConfig();
      
      // Create real Qdrant Cloud client
      qdrantClient = new QdrantClient({
        url: config.url,
        apiKey: config.apiKey,
        checkCompatibility: false, // Skip version check for compatibility
      });
      
      // Test connection
      await qdrantClient.getCollections();
      
      console.log('✅ Qdrant Cloud connection established successfully');
      console.log(`   - Endpoint: ${config.url.replace(/\/\/.+@/, '//***@')}`);
      console.log('   - API Key: configured ✓');
      
    } catch (error) {
      console.error('❌ Failed to connect to Qdrant Cloud:', error);
      throw new QdrantConnectionError('Qdrant Cloud connection failed', error as Error);
    }
  }

  return qdrantClient;
}



/**
 * Generate collection name from FalkorDB graph name
 * Convention: Use graph name directly as collection name
 */
export function getCollectionName(graphName: string): string {
  // Sanitize graph name for Qdrant collection naming
  const sanitized = graphName
    .toLowerCase()
    .replace(/[^a-z0-9_-]/g, '_') // Replace invalid chars with underscore
    .replace(/_{2,}/g, '_') // Replace multiple underscores with single
    .replace(/^_|_$/g, ''); // Remove leading/trailing underscores

  if (!sanitized) {
    throw new Error(`Invalid graph name: ${graphName}`);
  }

  return sanitized;
}

/**
 * Create collection for a specific graph with HR-optimized schema
 */
export async function createCollection(graphName: string, forceRecreate: boolean = false): Promise<boolean> {
  try {
    const client = await getQdrantClient();
    const collectionName = getCollectionName(graphName);

    // Check if collection already exists
    try {
      const existingCollection = await client.getCollection(collectionName);
      if (existingCollection && !forceRecreate) {
        console.log(`📊 Collection '${collectionName}' already exists for graph '${graphName}'`);
        return true;
      } else if (existingCollection && forceRecreate) {
        console.log(`🗑️ Force recreating collection '${collectionName}' for graph '${graphName}'`);
        await client.deleteCollection(collectionName);
        console.log(`✅ Deleted existing collection '${collectionName}'`);
      }
    } catch {
      // Collection doesn't exist, continue with creation
    }

    // Create collection with HR-optimized configuration using named vectors
    const collectionConfig = {
      vectors: {
        dense: {
          size: 1024, // Voyage AI voyage-3.5 default dimension
          distance: 'Cosine' as const, // Cosine similarity for semantic search
        }
      },
      sparse_vectors: {
        sparse: {} // Sparse vectors use default config (dot product distance)
      }
    };

    await client.createCollection(collectionName, collectionConfig);
    
    console.log(`✅ Created collection '${collectionName}' for graph '${graphName}'`);
    console.log(`   - Dense vectors: 1024 dims (Voyage AI compatible, Cosine similarity)`);
    console.log(`   - Sparse vectors: 30522 dims (SPLADE compatible, Dot product)`);
    console.log(`   - Optimized for: HR content hybrid search`);
    
    return true;

  } catch (error) {
    console.error(`❌ Failed to create collection for graph '${graphName}':`, error);
    throw error;
  }
}

/**
 * Get collection information
 */
export async function getCollectionInfo(graphName: string): Promise<CollectionInfo | null> {
  try {
    const client = await getQdrantClient();
    const collectionName = getCollectionName(graphName);

    const collection = await client.getCollection(collectionName);
    
    if (!collection) {
      return null;
    }

    // Safely extract vector configuration
    const vectorConfig = collection.config?.params?.vectors;
    let vectorSize = 1024;
    let distance = 'Cosine';

    if (vectorConfig) {
      // Handle named vector configurations (modern approach)
      if (typeof vectorConfig === 'object' && !('size' in vectorConfig)) {
        // Named vectors - look for 'dense' vector first
        if (vectorConfig.dense) {
          const denseVector = vectorConfig.dense;
          if (typeof denseVector === 'object' && 'size' in denseVector) {
            vectorSize = typeof denseVector.size === 'number' ? denseVector.size : 1024;
            distance = typeof denseVector.distance === 'string' ? denseVector.distance : 'Cosine';
          }
        } else {
          // Fallback to first available named vector
          const firstVectorName = Object.keys(vectorConfig)[0];
          if (firstVectorName && vectorConfig[firstVectorName]) {
            const firstVector = vectorConfig[firstVectorName];
            if (typeof firstVector === 'object' && 'size' in firstVector) {
              vectorSize = typeof firstVector.size === 'number' ? firstVector.size : 1024;
              distance = typeof firstVector.distance === 'string' ? firstVector.distance : 'Cosine';
            }
          }
        }
      } else if (typeof vectorConfig === 'object' && 'size' in vectorConfig) {
        // Legacy single vector config
        vectorSize = typeof vectorConfig.size === 'number' ? vectorConfig.size : 1024;
        distance = typeof vectorConfig.distance === 'string' ? vectorConfig.distance : 'Cosine';
      }
    }

    return {
      name: collectionName,
      graphName,
      vectorsCount: collection.points_count || 0,
      status: collection.status || 'unknown',
      vectorSize,
      distance,
    };

  } catch (error) {
    console.error(`❌ Failed to get collection info for graph '${graphName}':`, error);
    return null;
  }
}

/**
 * List all collections
 */
export async function listCollections(): Promise<string[]> {
  try {
    const client = await getQdrantClient();
    const collections = await client.getCollections();
    
    return collections.collections?.map((c: any) => c.name) || [];

  } catch (error) {
    console.error('❌ Failed to list collections:', error);
    throw error;
  }
}

/**
 * Delete collection for a specific graph
 */
export async function deleteCollection(graphName: string): Promise<boolean> {
  try {
    const client = await getQdrantClient();
    const collectionName = getCollectionName(graphName);

    await client.deleteCollection(collectionName);
    
    console.log(`🗑️ Deleted collection '${collectionName}' for graph '${graphName}'`);
    return true;

  } catch (error) {
    console.error(`❌ Failed to delete collection for graph '${graphName}':`, error);
    throw error;
  }
}

/**
 * Health check for Qdrant Cloud connection
 */
export async function healthCheck(): Promise<{ status: string; message: string; details?: any }> {
  try {
    const client = await getQdrantClient();
    
    // Test basic connectivity
    const collections = await client.getCollections();
    
    return {
      status: 'healthy',
      message: 'Qdrant Cloud connection is working',
      details: {
        collectionsCount: collections.collections?.length || 0,
        endpoint: process.env.QDRANT_URL?.replace(/\/\/.+@/, '//***@'), // Hide credentials
      }
    };

  } catch (error) {
    return {
      status: 'unhealthy',
      message: `Qdrant Cloud connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      details: error
    };
  }
}

/**
 * Close Qdrant connection (cleanup)
 */
export function closeConnection(): void {
  if (qdrantClient) {
    qdrantClient = null;
    console.log('🔌 Qdrant Cloud connection closed');
  }
}

/**
 * Get collection configuration template for HR data
 */
export function getHRCollectionConfig() {
  return {
    vectors: {
      dense: {
        size: 1024, // Voyage AI voyage-3.5 embedding dimension
        distance: 'Cosine', // Best for semantic similarity
      },
      // Future sparse vector configuration
      // sparse: {
      //   modifier: 'idf',
      //   distance: 'Dot',
      // }
    },
    optimizers_config: {
      default_segment_number: 2,
      max_segment_size: 20000,
      memmap_threshold: 50000,
    },
    hnsw_config: {
      m: 16, // Balanced performance/memory
      ef_construct: 100, // Good search quality
    },
  };
}

/**
 * Create full-text indexes for exact phrase matching
 * Sets up indexes for all content fields: title, content, summary, fullContent
 */
export async function createFullTextIndexes(collectionName: string): Promise<void> {
  const client = await getQdrantClient();

  console.log(`📝 Setting up full-text indexes for collection: ${collectionName}`);

  const indexConfigs = [
    {
      field_name: "title",
      description: "Index for node and article titles"
    },
    {
      field_name: "content", 
      description: "Index for node content"
    },
    {
      field_name: "summary",
      description: "Index for article summaries"
    },
    {
      field_name: "fullContent",
      description: "Index for full article content"
    }
  ];

  for (const config of indexConfigs) {
    try {
      console.log(`   🔍 Creating index for field: ${config.field_name}`);
      
      await client.createPayloadIndex(collectionName, {
        field_name: config.field_name,
        field_schema: {
          type: "text",
          tokenizer: "word",
          min_token_len: 2,
          max_token_len: 50,
          lowercase: true
        }
      });

      console.log(`   ✅ Successfully created index for ${config.field_name}`);
    } catch (error) {
      // Index might already exist - this is okay
      if (error && typeof error === 'object' && 'message' in error) {
        const errorMessage = (error as Error).message;
        if (errorMessage.includes('already exists') || errorMessage.includes('Already exists')) {
          console.log(`   📋 Index for ${config.field_name} already exists - skipping`);
        } else {
          console.warn(`   ⚠️ Failed to create index for ${config.field_name}:`, errorMessage);
        }
      } else {
        console.warn(`   ⚠️ Failed to create index for ${config.field_name}:`, error);
      }
    }
  }

  console.log(`✅ Full-text index setup completed for ${collectionName}`);
}

/**
 * List all payload indexes for a collection
 */
export async function listPayloadIndexes(collectionName: string): Promise<any> {
  const client = await getQdrantClient();
  
  try {
    const response = await client.getCollection(collectionName);
    return (response.config?.params as any)?.payload_indexes || {};
  } catch (error) {
    console.error(`Failed to get payload indexes for ${collectionName}:`, error);
    return {};
  }
} 