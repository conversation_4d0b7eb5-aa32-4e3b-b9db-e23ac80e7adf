/**
 * Sample data import script for FalkorDB
 * This script imports the existing mockdata.json into FalkorDB
 * Run with: node falkordTest/import-sample-data.js
 */

const fs = require("fs");
const path = require("path");

const BASE_URL = "http://localhost:3001";
const GRAPH_NAME = "knowledge_graph";

class DataImporter {
  constructor() {
    this.graphName = GRAPH_NAME;
  }

  async makeRequest(url, options = {}) {
    try {
      const fetch = (await import("node-fetch")).default;
      const response = await fetch(url, options);
      const data = await response.json();
      return { response, data };
    } catch (error) {
      console.log(
        "Note: Install node-fetch for better testing: pnpm install node-fetch"
      );
      return {
        response: { ok: false },
        data: { error: "Fetch not available" },
      };
    }
  }

  async loadMockData() {
    try {
      const mockDataPath = path.join(__dirname, "..", "mockdata.json");
      const rawData = fs.readFileSync(mockDataPath, "utf8");
      return JSON.parse(rawData);
    } catch (error) {
      console.error("❌ Failed to load mockdata.json:", error.message);
      throw error;
    }
  }

  async createGraph() {
    console.log("📊 Creating graph...");

    try {
      const { response, data } = await this.makeRequest(
        `${BASE_URL}/api/falkor/graph`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            graphName: this.graphName,
            overwrite: true,
          }),
        }
      );

      if (response.ok && data.success) {
        console.log(`✅ Graph '${this.graphName}' created successfully`);
        return true;
      } else {
        console.error("❌ Failed to create graph:", data.error || data.message);
        return false;
      }
    } catch (error) {
      console.error("❌ Error creating graph:", error.message);
      return false;
    }
  }

  async importData(mockData) {
    console.log("📥 Importing data into graph...");

    try {
      const { response, data } = await this.makeRequest(
        `${BASE_URL}/api/falkor/nodes`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            graphName: this.graphName,
            operation: "create",
            nodeType: "all",
            data: mockData,
          }),
        }
      );

      if (response.ok && data.success) {
        console.log("✅ Data imported successfully");
        console.log("📈 Import summary:", data.summary);
        return true;
      } else {
        console.error("❌ Failed to import data:", data.error || data.message);
        return false;
      }
    } catch (error) {
      console.error("❌ Error importing data:", error.message);
      return false;
    }
  }

  async getGraphStats() {
    console.log("📊 Getting graph statistics...");

    try {
      const { response, data } = await this.makeRequest(
        `${BASE_URL}/api/falkor/graph?action=stats&name=${this.graphName}`
      );

      if (response.ok && data.success) {
        console.log("✅ Graph statistics:");
        console.log(`   - Nodes: ${data.stats.nodeCount}`);
        console.log(`   - Edges: ${data.stats.edgeCount}`);
        console.log(`   - Graph: ${data.stats.graphName}`);
        return true;
      } else {
        console.error(
          "❌ Failed to get graph stats:",
          data.error || data.message
        );
        return false;
      }
    } catch (error) {
      console.error("❌ Error getting graph stats:", error.message);
      return false;
    }
  }

  async testHealthCheck() {
    console.log("🏥 Checking FalkorDB health...");

    try {
      const { response, data } = await this.makeRequest(
        `${BASE_URL}/api/falkor/graph?action=health`
      );

      if (response.ok && data.status === "healthy") {
        console.log("✅ FalkorDB is healthy");
        return true;
      } else {
        console.error("❌ FalkorDB health check failed:", data.message);
        return false;
      }
    } catch (error) {
      console.error("❌ Error checking health:", error.message);
      return false;
    }
  }

  async run() {
    console.log("🚀 Starting data import process...\n");

    try {
      // Step 1: Health check
      const healthOk = await this.testHealthCheck();
      if (!healthOk) {
        console.log(
          "\n❌ Health check failed. Make sure FalkorDB is running and the server is started."
        );
        return;
      }

      // Step 2: Load mock data
      console.log("\n📂 Loading mock data...");
      const mockData = await this.loadMockData();
      console.log("✅ Mock data loaded successfully");

      // Step 3: Create graph
      console.log("\n📊 Setting up graph...");
      const graphCreated = await this.createGraph();
      if (!graphCreated) {
        console.log("\n❌ Failed to create graph. Aborting import.");
        return;
      }

      // Step 4: Import data
      console.log("\n📥 Importing data...");
      const dataImported = await this.importData(mockData);
      if (!dataImported) {
        console.log("\n❌ Failed to import data.");
        return;
      }

      // Step 5: Get final statistics
      console.log("\n📊 Final statistics...");
      await this.getGraphStats();

      console.log("\n🎉 Data import completed successfully!");
      console.log(
        `\n💡 You can now query the graph '${this.graphName}' using the FalkorDB API endpoints.`
      );
    } catch (error) {
      console.error("\n❌ Import process failed:", error.message);
    }
  }
}

// Example usage functions
function printUsageExamples() {
  console.log("\n📚 Usage Examples:");
  console.log("==================\n");

  console.log("1. Check graph health:");
  console.log(`   curl "${BASE_URL}/api/falkor/graph?action=health"\n`);

  console.log("2. Get graph statistics:");
  console.log(
    `   curl "${BASE_URL}/api/falkor/graph?action=stats&name=${GRAPH_NAME}"\n`
  );

  console.log("3. Create a new category node:");
  console.log(`   curl -X POST "${BASE_URL}/api/falkor/nodes" \\`);
  console.log('     -H "Content-Type: application/json" \\');
  console.log("     -d '{\n");
  console.log(`       "graphName": "${GRAPH_NAME}",\n`);
  console.log('       "operation": "create",\n');
  console.log('       "data": {\n');
  console.log('         "id": "new-cat-1",\n');
  console.log('         "name": "New Category",\n');
  console.log('         "country": "global",\n');
  console.log('         "summary": "A new category",\n');
  console.log('         "refinedSummary": "Detailed summary",\n');
  console.log('         "nodeType": "category",\n');
  console.log('         "level": 0,\n');
  console.log('         "path": "New Category"\n');
  console.log("       }\n");
  console.log("     }'\n");

  console.log("4. Upsert a record node:");
  console.log(`   curl -X POST "${BASE_URL}/api/falkor/nodes" \\`);
  console.log('     -H "Content-Type: application/json" \\');
  console.log("     -d '{\n");
  console.log(`       "graphName": "${GRAPH_NAME}",\n`);
  console.log('       "operation": "upsert",\n');
  console.log('       "data": {\n');
  console.log('         "id": "new-rec-1",\n');
  console.log('         "recordId": 123456,\n');
  console.log('         "title": "New Record",\n');
  console.log('         "htmlUrl": "https://example.com",\n');
  console.log('         "breadcrumb": "Category > Record",\n');
  console.log('         "isDeepest": true,\n');
  console.log('         "country": "global",\n');
  console.log('         "sectionId": 789012,\n');
  console.log('         "cleanedBody": "Record content",\n');
  console.log('         "nodeType": "record"\n');
  console.log("       }\n");
  console.log("     }'\n");
}

// Run the importer
if (require.main === module) {
  const importer = new DataImporter();
  importer
    .run()
    .then(() => {
      printUsageExamples();
    })
    .catch(console.error);
}

module.exports = { DataImporter };
