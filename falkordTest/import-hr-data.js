/**
 * HR Data import script for FalkorDB
 * This script imports the mockHRdata.json into FalkorDB
 * Run with: node falkordTest/import-hr-data.js
 */

const fs = require("fs");
const path = require("path");

const BASE_URL = "http://localhost:3000";
const HR_GRAPH_NAME = "mock_hr";

class HRDataImporter {
  constructor() {
    this.graphName = HR_GRAPH_NAME;
  }

  async makeRequest(url, options = {}) {
    try {
      const fetch = (await import("node-fetch")).default;
      const response = await fetch(url, options);
      const data = await response.json();
      return { response, data };
    } catch (error) {
      console.log(
        "Note: Install node-fetch for better testing: pnpm install node-fetch"
      );
      return {
        response: { ok: false },
        data: { error: "Fetch not available" },
      };
    }
  }

  async loadHRData() {
    try {
      const hrDataPath = path.join(__dirname, "mockHRdata.json");
      const rawData = fs.readFileSync(hrDataPath, "utf8");
      return JSON.parse(rawData);
    } catch (error) {
      console.error("❌ Failed to load mockHRdata.json:", error.message);
      throw error;
    }
  }

  async createGraph() {
    console.log("📊 Creating HR graph...");

    try {
      const { response, data } = await this.makeRequest(
        `${BASE_URL}/api/falkor/graph`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            graphName: this.graphName,
            overwrite: true,
          }),
        }
      );

      if (response.ok && data.success) {
        console.log(`✅ Graph '${this.graphName}' created successfully`);
        return true;
      } else {
        console.error("❌ Failed to create graph:", data.error || data.message);
        return false;
      }
    } catch (error) {
      console.error("❌ Error creating graph:", error.message);
      return false;
    }
  }

  async importHRData(hrData) {
    console.log("📥 Importing HR data into graph...");

    try {
      const { response, data } = await this.makeRequest(
        `${BASE_URL}/api/falkor/nodes`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            graphName: this.graphName,
            operation: "create_hr",
            nodeType: "all",
            data: hrData,
          }),
        }
      );

      if (response.ok && data.success) {
        console.log("✅ HR data imported successfully");
        console.log("📈 Import summary:", data.summary);
        return true;
      } else {
        console.error(
          "❌ Failed to import HR data:",
          data.error || data.message
        );
        return false;
      }
    } catch (error) {
      console.error("❌ Error importing HR data:", error.message);
      return false;
    }
  }

  async getGraphStats() {
    console.log("📊 Getting HR graph statistics...");

    try {
      const { response, data } = await this.makeRequest(
        `${BASE_URL}/api/falkor/graph?action=stats&name=${this.graphName}`
      );

      if (response.ok && data.success) {
        console.log("✅ HR Graph statistics:");
        console.log(`   - Nodes: ${data.stats.nodeCount}`);
        console.log(`   - Edges: ${data.stats.edgeCount}`);
        console.log(`   - Graph: ${data.stats.graphName}`);
        return true;
      } else {
        console.error(
          "❌ Failed to get graph stats:",
          data.error || data.message
        );
        return false;
      }
    } catch (error) {
      console.error("❌ Error getting graph stats:", error.message);
      return false;
    }
  }

  async testHealthCheck() {
    console.log("🏥 Checking FalkorDB health...");

    try {
      const { response, data } = await this.makeRequest(
        `${BASE_URL}/api/falkor/graph?action=health`
      );

      if (response.ok && data.status === "healthy") {
        console.log("✅ FalkorDB is healthy");
        return true;
      } else {
        console.error("❌ FalkorDB health check failed:", data.message);
        return false;
      }
    } catch (error) {
      console.error("❌ Error checking health:", error.message);
      return false;
    }
  }

  async run() {
    console.log("🚀 Starting HR data import process...\n");

    try {
      // Step 1: Health check
      const healthOk = await this.testHealthCheck();
      if (!healthOk) {
        console.log(
          "\n❌ Health check failed. Make sure FalkorDB is running and the server is started."
        );
        return;
      }

      // Step 2: Load HR data
      console.log("\n📂 Loading HR data...");
      const hrData = await this.loadHRData();
      console.log("✅ HR data loaded successfully");
      console.log(`📄 Found ${hrData.length} root sections`);

      // Step 3: Create graph
      console.log("\n📊 Setting up HR graph...");
      const graphCreated = await this.createGraph();
      if (!graphCreated) {
        console.log("\n❌ Failed to create graph. Aborting import.");
        return;
      }

      // Step 4: Import data
      console.log("\n📥 Importing HR data...");
      const dataImported = await this.importHRData(hrData);
      if (!dataImported) {
        console.log("\n❌ Failed to import HR data.");
        return;
      }

      // Step 5: Get final statistics
      console.log("\n📊 Final statistics...");
      await this.getGraphStats();

      console.log("\n🎉 HR data import completed successfully!");
      console.log(
        `\n💡 You can now query the HR graph '${this.graphName}' using the FalkorDB API endpoints.`
      );
      console.log(
        `\n🔗 Test the API: curl "${BASE_URL}/api/data-falkor?graph=${this.graphName}"`
      );
    } catch (error) {
      console.error("\n❌ HR import process failed:", error.message);
    }
  }
}

// Example usage functions
function printHRUsageExamples() {
  console.log("\n📚 HR Data Usage Examples:");
  console.log("==========================\n");

  console.log("1. Check HR graph health:");
  console.log(`   curl "${BASE_URL}/api/falkor/graph?action=health"\n`);

  console.log("2. Get HR graph statistics:");
  console.log(
    `   curl "${BASE_URL}/api/falkor/graph?action=stats&name=${HR_GRAPH_NAME}"\n`
  );

  console.log("3. Query HR data via API:");
  console.log(`   curl "${BASE_URL}/api/data-falkor?graph=${HR_GRAPH_NAME}"\n`);

  console.log("4. Create a new HR category:");
  console.log(`   curl -X POST "${BASE_URL}/api/falkor/nodes" \\`);
  console.log('     -H "Content-Type: application/json" \\');
  console.log("     -d '{\n");
  console.log(`       "graphName": "${HR_GRAPH_NAME}",\n`);
  console.log('       "operation": "create",\n');
  console.log('       "data": {\n');
  console.log('         "id": "hr-new-category-1",\n');
  console.log('         "name": "New HR Category",\n');
  console.log('         "country": "hr",\n');
  console.log('         "summary": "A new HR category",\n');
  console.log('         "refinedSummary": "Detailed summary for HR",\n');
  console.log('         "nodeType": "category",\n');
  console.log('         "level": 0,\n');
  console.log('         "path": "New HR Category"\n');
  console.log("       }\n");
  console.log("     }'\n");
}

// Run the HR importer
if (require.main === module) {
  const importer = new HRDataImporter();
  importer
    .run()
    .then(() => {
      printHRUsageExamples();
    })
    .catch(console.error);
}

module.exports = { HRDataImporter };
