# FalkorDB Testing Suite

This folder contains comprehensive testing scripts for the FalkorDB integration. These scripts test graph creation, node operations, data import, and API endpoints.

## Prerequisites

1. **FalkorDB Server**: Make sure FalkorDB is running and accessible
2. **Next.js Application**: Start the development server with `pnpm run dev`
3. **Node.js**: Required to run the test scripts
4. **Optional**: Install `node-fetch` for better HTTP request handling:
   ```bash
   pnpm install node-fetch
   ```

## Environment Setup

Create a `.env.local` file in the project root with FalkorDB configuration:

```env
FALKOR_HOST=localhost
FALKOR_PORT=6379
FALKOR_PASSWORD=your_password_if_required
FALKOR_USERNAME=your_username_if_required
FALKOR_DATABASE=0
```

## Test Scripts

### 1. Basic API Tests (`run-tests.js`)

Tests all basic API endpoints and operations.

```bash
node falkordTest/run-tests.js
```

**What it tests:**

- Health check
- Graph creation and deletion
- Single node creation (category and record)
- Node upsert operations
- Bulk data import
- Graph statistics

### 2. Sample Data Import (`import-sample-data.js`)

Imports the existing `mockdata.json` into FalkorDB.

```bash
node falkordTest/import-sample-data.js
```

**What it does:**

- Loads `mockdata.json`
- Creates a new graph called `knowledge_graph`
- Imports all categories and records
- Creates relationships between nodes
- Displays import statistics

### 3. Integration Tests (`integration-tests.js`)

Comprehensive integration testing suite.

```bash
node falkordTest/integration-tests.js
```

**What it tests:**

- Graph lifecycle management
- Data integrity verification
- Concurrent operations
- Error handling
- Relationship creation
- Complex bulk operations

### 4. TypeScript Test Suite (`test-graph-operations.ts`)

TypeScript version of the test suite (requires compilation).

```bash
# Compile and run (if using TypeScript)
npx tsc falkordTest/test-graph-operations.ts
node falkordTest/test-graph-operations.js
```

## API Endpoints

### Graph Management

#### Create Graph

```bash
curl -X POST "http://localhost:3000/api/falkor/graph" \
  -H "Content-Type: application/json" \
  -d '{
    "graphName": "my_graph",
    "overwrite": false
  }'
```

#### Check Graph Health

```bash
curl "http://localhost:3000/api/falkor/graph?action=health"
```

#### Check if Graph Exists

```bash
curl "http://localhost:3000/api/falkor/graph?action=exists&name=my_graph"
```

#### Get Graph Statistics

```bash
curl "http://localhost:3000/api/falkor/graph?action=stats&name=my_graph"
```

#### Delete Graph

```bash
curl -X DELETE "http://localhost:3000/api/falkor/graph?name=my_graph"
```

### Node Operations

#### Create Single Category Node

```bash
curl -X POST "http://localhost:3000/api/falkor/nodes" \
  -H "Content-Type: application/json" \
  -d '{
    "graphName": "my_graph",
    "operation": "create",
    "data": {
      "id": "cat-1",
      "name": "My Category",
      "country": "global",
      "summary": "Category summary",
      "refinedSummary": "Detailed summary",
      "nodeType": "category",
      "level": 0,
      "path": "My Category"
    }
  }'
```

#### Create Single Record Node

```bash
curl -X POST "http://localhost:3000/api/falkor/nodes" \
  -H "Content-Type: application/json" \
  -d '{
    "graphName": "my_graph",
    "operation": "create",
    "data": {
      "id": "rec-1",
      "recordId": 12345,
      "title": "My Record",
      "htmlUrl": "https://example.com",
      "breadcrumb": "Category > Record",
      "isDeepest": true,
      "country": "global",
      "sectionId": 67890,
      "cleanedBody": "Record content",
      "nodeType": "record"
    }
  }'
```

#### Upsert Node

```bash
curl -X POST "http://localhost:3000/api/falkor/nodes" \
  -H "Content-Type: application/json" \
  -d '{
    "graphName": "my_graph",
    "operation": "upsert",
    "data": {
      "id": "existing-node-id",
      "name": "Updated Name",
      "nodeType": "category",
      ...
    }
  }'
```

#### Bulk Import

```bash
curl -X POST "http://localhost:3000/api/falkor/nodes" \
  -H "Content-Type: application/json" \
  -d '{
    "graphName": "my_graph",
    "operation": "create",
    "nodeType": "all",
    "data": {
      "children": {
        "Category Name": {
          "id": "cat-1",
          "country": "global",
          "summary": "Summary",
          "refinedSummary": "Refined summary",
          "children": {},
          "records": [...]
        }
      }
    }
  }'
```

## Data Structure

### Category Node

```json
{
  "id": "unique-category-id",
  "name": "Category Name",
  "country": "global",
  "summary": "Brief summary",
  "refinedSummary": "Detailed summary",
  "nodeType": "category",
  "level": 0,
  "path": "Category Name"
}
```

### Record Node

```json
{
  "id": "unique-record-id",
  "recordId": 12345,
  "title": "Record Title",
  "htmlUrl": "https://example.com",
  "breadcrumb": "Category > Record",
  "isDeepest": true,
  "country": "global",
  "sectionId": 67890,
  "cleanedBody": "Record content",
  "nodeType": "record"
}
```

## Troubleshooting

### Common Issues

1. **Connection Failed**

   - Ensure FalkorDB is running
   - Check connection parameters in `.env.local`
   - Verify network connectivity

2. **Graph Already Exists**

   - Use `overwrite: true` when creating graphs
   - Or delete the existing graph first

3. **Invalid Node Data**

   - Ensure all required fields are present
   - Check data types match the schema

4. **Server Not Running**
   - Start the Next.js development server: `pnpm run dev`
   - Ensure it's running on port 3000

### Debug Mode

Add debug logging by setting environment variable:

```bash
DEBUG=falkordb node falkordTest/run-tests.js
```

## Test Results

All test scripts provide detailed output including:

- ✅ Successful operations
- ❌ Failed operations with error details
- 📊 Statistics and summaries
- 💡 Usage examples and next steps

## Contributing

When adding new tests:

1. Follow the existing pattern
2. Include proper error handling
3. Add descriptive test names and messages
4. Update this README with new functionality
