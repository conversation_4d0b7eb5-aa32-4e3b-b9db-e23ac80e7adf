/**
 * Advanced Search API - Exact Matching Implementation
 * 
 * Task 2.1: Exact Matching Implementation
 * - Exact word matching algorithm for HR content
 * - Exact phrase matching algorithm 
 * - Configurable scoring mechanisms (TF-IDF, BM25-style)
 * - Case-insensitive matching with tokenization
 */

import type { HRSearchRecord, SearchResult, SearchSnippet } from './advanced-search/types';

/**
 * Exact matching configuration
 */
export interface ExactMatchingConfig {
  caseSensitive: boolean;
  stemming: boolean;
  stopWords: string[];
  scoreBoosts: {
    titleMatch: number;
    contentMatch: number;
    breadcrumbMatch: number;
  };
  snippet: {
    enabled: boolean;
    contextWords: number;
    maxSnippets: number;
  };
}

/**
 * Default configuration for HR content
 */
export const DEFAULT_EXACT_MATCHING_CONFIG: ExactMatchingConfig = {
  caseSensitive: false,
  stemming: false, // Simple implementation without stemming for now
  stopWords: ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'],
  scoreBoosts: {
    titleMatch: 3.0,    // Title matches are most important
    contentMatch: 1.0,   // Base score for content matches
    breadcrumbMatch: 2.0 // Section path matches are important for HR navigation
  },
  snippet: {
    enabled: true,
    contextWords: 10,
    maxSnippets: 3
  }
};

/**
 * Tokenize text for exact matching
 */
function tokenizeText(text: string, config: ExactMatchingConfig): string[] {
  if (!text) return [];
  
  let processedText = config.caseSensitive ? text : text.toLowerCase();
  
  // Extract words (alphanumeric + some special chars)
  const tokens = processedText.match(/\b[\w'-]+\b/g) || [];
  
  // Remove stop words if configured
  if (config.stopWords.length > 0) {
    return tokens.filter(token => !config.stopWords.includes(token));
  }
  
  return tokens;
}

/**
 * Calculate TF-IDF style score for exact word matches
 */
function calculateWordMatchScore(
  queryTokens: string[],
  documentTokens: string[],
  documentLength: number,
  totalDocuments: number = 1000 // Approximate corpus size
): number {
  if (queryTokens.length === 0 || documentTokens.length === 0) return 0;
  
  let score = 0;
  const documentTokenSet = new Set(documentTokens);
  
  for (const queryToken of queryTokens) {
    if (documentTokenSet.has(queryToken)) {
      // Term frequency (normalized by document length)
      const tf = documentTokens.filter(token => token === queryToken).length / documentLength;
      
      // Inverse document frequency (simplified)
      const idf = Math.log(totalDocuments / (1 + 1)); // Simplified since we don't have corpus stats
      
      score += tf * idf;
    }
  }
  
  // Normalize by query length
  return score / queryTokens.length;
}

/**
 * Find exact phrase matches in text
 */
function findPhraseMatches(phrase: string, text: string, caseSensitive: boolean = false): Array<{start: number, end: number}> {
  if (!phrase || !text) return [];
  
  const searchPhrase = caseSensitive ? phrase : phrase.toLowerCase();
  const searchText = caseSensitive ? text : text.toLowerCase();
  
  const matches: Array<{start: number, end: number}> = [];
  let startIndex = 0;
  
  while (true) {
    const index = searchText.indexOf(searchPhrase, startIndex);
    if (index === -1) break;
    
    matches.push({
      start: index,
      end: index + phrase.length
    });
    
    startIndex = index + 1;
  }
  
  return matches;
}

/**
 * Extract snippets around phrase matches
 */
function extractSnippets(
  text: string,
  matches: Array<{start: number, end: number}>,
  contextWords: number,
  maxSnippets: number
): SearchSnippet[] {
  if (!text || matches.length === 0) return [];
  
  const snippets: SearchSnippet[] = [];
  const words = text.split(/\s+/);
  
  // Sort matches by position and take top N
  const sortedMatches = matches
    .sort((a, b) => a.start - b.start)
    .slice(0, maxSnippets);
  
  for (const match of sortedMatches) {
    // Find word boundaries around the match
    const beforeText = text.substring(0, match.start);
    const afterText = text.substring(match.end);
    
    const beforeWords = beforeText.split(/\s+/).slice(-contextWords);
    const afterWords = afterText.split(/\s+/).slice(0, contextWords);
    
    const contextBefore = beforeWords.join(' ');
    const contextAfter = afterWords.join(' ');
    const matchText = text.substring(match.start, match.end);
    
    snippets.push({
      text: matchText,
      context_before: contextBefore,
      context_after: contextAfter,
      start_position: match.start,
      end_position: match.end,
      relevance_score: 1.0 // Simple scoring for now
    });
  }
  
  return snippets;
}

/**
 * Exact word matching implementation
 */
export function exactWordMatch(
  query: string,
  records: HRSearchRecord[],
  config: ExactMatchingConfig = DEFAULT_EXACT_MATCHING_CONFIG
): SearchResult[] {
  const queryTokens = tokenizeText(query, config);
  if (queryTokens.length === 0) return [];
  
  const results: SearchResult[] = [];
  
  for (const record of records) {
    let totalScore = 0;
    const methodContributions: { [method: string]: number } = {
      exact_words: 0
    };
    
    // Search in title
    const titleTokens = tokenizeText(record.title, config);
    const titleScore = calculateWordMatchScore(queryTokens, titleTokens, titleTokens.length);
    if (titleScore > 0) {
      totalScore += titleScore * config.scoreBoosts.titleMatch;
    }
    
    // Search in content
    const contentText = [record.content, record.summary, record.fullContent].filter(Boolean).join(' ');
    const contentTokens = tokenizeText(contentText, config);
    const contentScore = calculateWordMatchScore(queryTokens, contentTokens, contentTokens.length);
    if (contentScore > 0) {
      totalScore += contentScore * config.scoreBoosts.contentMatch;
    }
    
    // Search in breadcrumb/section path
    const breadcrumbTokens = tokenizeText(record.breadcrumb, config);
    const breadcrumbScore = calculateWordMatchScore(queryTokens, breadcrumbTokens, breadcrumbTokens.length);
    if (breadcrumbScore > 0) {
      totalScore += breadcrumbScore * config.scoreBoosts.breadcrumbMatch;
    }
    
    // Only include results with matches
    if (totalScore > 0) {
      methodContributions.exact_words = totalScore;
      
      results.push({
        record,
        relevance_score: totalScore,
        method_contributions: methodContributions,
        snippets: [] // Word matching doesn't generate snippets
      });
    }
  }
  
  // Sort by relevance score
  return results.sort((a, b) => b.relevance_score - a.relevance_score);
}

/**
 * Exact phrase matching implementation
 */
export function exactPhraseMatch(
  query: string,
  records: HRSearchRecord[],
  config: ExactMatchingConfig = DEFAULT_EXACT_MATCHING_CONFIG
): SearchResult[] {
  if (!query.trim()) return [];
  
  const results: SearchResult[] = [];
  const trimmedQuery = query.trim();
  
  for (const record of records) {
    let totalScore = 0;
    let allSnippets: SearchSnippet[] = [];
    const methodContributions: { [method: string]: number } = {
      exact_phrase: 0
    };
    
    // Search in title
    const titleMatches = findPhraseMatches(trimmedQuery, record.title, config.caseSensitive);
    if (titleMatches.length > 0) {
      totalScore += titleMatches.length * config.scoreBoosts.titleMatch;
      
      if (config.snippet.enabled) {
        const titleSnippets = extractSnippets(
          record.title,
          titleMatches,
          config.snippet.contextWords,
          config.snippet.maxSnippets
        );
        allSnippets.push(...titleSnippets);
      }
    }
    
    // Search in content
    const contentText = [record.content, record.summary, record.fullContent].filter(Boolean).join(' ');
    if (contentText) {
      const contentMatches = findPhraseMatches(trimmedQuery, contentText, config.caseSensitive);
      if (contentMatches.length > 0) {
        // Score based on frequency and document length
        const contentScore = (contentMatches.length * config.scoreBoosts.contentMatch) / Math.sqrt(contentText.length);
        totalScore += contentScore;
        
        if (config.snippet.enabled) {
          const contentSnippets = extractSnippets(
            contentText,
            contentMatches,
            config.snippet.contextWords,
            config.snippet.maxSnippets
          );
          allSnippets.push(...contentSnippets);
        }
      }
    }
    
    // Search in breadcrumb
    const breadcrumbMatches = findPhraseMatches(trimmedQuery, record.breadcrumb, config.caseSensitive);
    if (breadcrumbMatches.length > 0) {
      totalScore += breadcrumbMatches.length * config.scoreBoosts.breadcrumbMatch;
      
      if (config.snippet.enabled) {
        const breadcrumbSnippets = extractSnippets(
          record.breadcrumb,
          breadcrumbMatches,
          config.snippet.contextWords,
          config.snippet.maxSnippets
        );
        allSnippets.push(...breadcrumbSnippets);
      }
    }
    
    // Only include results with matches
    if (totalScore > 0) {
      methodContributions.exact_phrase = totalScore;
      
      // Sort snippets by relevance and limit
      allSnippets.sort((a, b) => b.relevance_score - a.relevance_score);
      allSnippets = allSnippets.slice(0, config.snippet.maxSnippets);
      
      results.push({
        record,
        relevance_score: totalScore,
        method_contributions: methodContributions,
        snippets: allSnippets
      });
    }
  }
  
  // Sort by relevance score
  return results.sort((a, b) => b.relevance_score - a.relevance_score);
}

/**
 * Combined exact matching (words + phrases)
 */
export function combinedExactMatch(
  query: string,
  records: HRSearchRecord[],
  wordWeight: number = 0.4,
  phraseWeight: number = 0.6,
  config: ExactMatchingConfig = DEFAULT_EXACT_MATCHING_CONFIG
): SearchResult[] {
  const wordResults = exactWordMatch(query, records, config);
  const phraseResults = exactPhraseMatch(query, records, config);
  
  // Combine results by record ID
  const combinedResults = new Map<string, SearchResult>();
  
  // Add word match results
  for (const result of wordResults) {
    const existing = combinedResults.get(result.record.id);
    if (existing) {
      existing.relevance_score += result.relevance_score * wordWeight;
      existing.method_contributions = {
        ...existing.method_contributions,
        ...result.method_contributions
      };
    } else {
      combinedResults.set(result.record.id, {
        ...result,
        relevance_score: result.relevance_score * wordWeight
      });
    }
  }
  
  // Add phrase match results
  for (const result of phraseResults) {
    const existing = combinedResults.get(result.record.id);
    if (existing) {
      existing.relevance_score += result.relevance_score * phraseWeight;
      existing.method_contributions = {
        ...existing.method_contributions,
        ...result.method_contributions
      };
      // Merge snippets
      if (result.snippets && result.snippets.length > 0) {
        existing.snippets = [...(existing.snippets || []), ...result.snippets];
      }
    } else {
      combinedResults.set(result.record.id, {
        ...result,
        relevance_score: result.relevance_score * phraseWeight
      });
    }
  }
  
  // Convert to array and sort
  const finalResults = Array.from(combinedResults.values());
  return finalResults.sort((a, b) => b.relevance_score - a.relevance_score);
}

/**
 * Exact matching utility functions for testing
 */
export const ExactMatchingUtils = {
  tokenizeText,
  findPhraseMatches,
  extractSnippets,
  calculateWordMatchScore
}; 