/**
 * Advanced Search API - Vector Similarity Implementation
 * 
 * Task 2.2: Vector Similarity Setup
 * - Voyage AI's voyage-3.5 model integration for embeddings
 * - Vector similarity search via Qdrant Cloud with cosine similarity
 * - Optimal embedding dimensions (1024 default, supports 256, 512, 2048)
 * - Proper input_type handling for queries vs documents
 * - Authentication using VOYAGE_API_KEY environment variable
 */

import { QdrantClient } from '@qdrant/js-client-rest';
import type { HRSearchRecord, SearchResult, VoyageEmbeddingResponse } from './advanced-search/types';
import { getQdrantClient, getCollectionName } from './advanced-search/qdrant-client';

/**
 * Vector similarity configuration
 */
export interface VectorSimilarityConfig {
  model: 'voyage-3.5';
  dimension: 256 | 512 | 1024 | 2048;
  inputType: 'query' | 'document';
  truncation: boolean;
  batchSize: number;
  scoreThreshold: number;
  maxResults: number;
}

/**
 * Default configuration for Voyage AI
 */
export const DEFAULT_VECTOR_CONFIG: VectorSimilarityConfig = {
  model: 'voyage-3.5',
  dimension: 1024,
  inputType: 'query',
  truncation: true,
  batchSize: 100,
  scoreThreshold: 0.1,
  maxResults: 100
};

/**
 * Voyage AI embedding generation
 */
export class VoyageEmbeddingService {
  private apiKey: string;
  private baseUrl: string = 'https://api.voyageai.com/v1';

  constructor(apiKey?: string) {
    this.apiKey = apiKey || process.env.VOYAGE_API_KEY || '';
    if (!this.apiKey) {
      throw new Error('VOYAGE_API_KEY environment variable is required');
    }
  }

  /**
   * Generate embeddings using Voyage AI API
   */
  async generateEmbeddings(
    texts: string[],
    config: Partial<VectorSimilarityConfig> = {}
  ): Promise<{ embeddings: number[][]; totalTokens: number }> {
    const finalConfig = { ...DEFAULT_VECTOR_CONFIG, ...config };
    
    if (texts.length === 0) {
      return { embeddings: [], totalTokens: 0 };
    }

    try {
      const requestBody = {
        input: texts,
        model: finalConfig.model,
        input_type: finalConfig.inputType,
        output_dimension: finalConfig.dimension,
        truncation: finalConfig.truncation
      };

      console.log(`🚀 Generating embeddings for ${texts.length} texts using ${finalConfig.model}`);
      
      const response = await fetch(`${this.baseUrl}/embeddings`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Voyage AI API error (${response.status}): ${errorText}`);
      }

      const data = await response.json();
      
      // Transform Voyage AI response format to our expected format
      const embeddings = data.data?.map((item: any) => item.embedding) || [];
      const totalTokens = data.usage?.total_tokens || 0;

      console.log(`✅ Generated ${embeddings.length} embeddings, ${totalTokens} tokens used`);
      
      return {
        embeddings,
        totalTokens
      };

    } catch (error) {
      console.error('❌ Voyage AI embedding generation failed:', error);
      throw new Error(`Failed to generate embeddings: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate single query embedding
   */
  async generateQueryEmbedding(
    query: string,
    dimension: number = 1024
  ): Promise<number[]> {
    const result = await this.generateEmbeddings([query], {
      inputType: 'query',
      dimension: dimension as any
    });
    
    return result.embeddings[0] || [];
  }

  /**
   * Generate batch embeddings for documents
   */
  async generateDocumentEmbeddings(
    documents: string[],
    dimension: number = 1024,
    batchSize: number = 100
  ): Promise<number[][]> {
    const embeddings: number[][] = [];
    
    // Process in batches to stay within API limits
    for (let i = 0; i < documents.length; i += batchSize) {
      const batch = documents.slice(i, i + batchSize);
      
      const result = await this.generateEmbeddings(batch, {
        inputType: 'document',
        dimension: dimension as any
      });
      
      embeddings.push(...result.embeddings);
      
      // Small delay between batches to respect rate limits
      if (i + batchSize < documents.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    return embeddings;
  }
}

/**
 * Vector similarity search implementation
 */
export class VectorSimilaritySearch {
  private embeddingService: VoyageEmbeddingService;
  private qdrantClient: QdrantClient | null = null;

  constructor(voyageApiKey?: string) {
    this.embeddingService = new VoyageEmbeddingService(voyageApiKey);
  }

  /**
   * Initialize Qdrant client
   */
  private async getQdrant(): Promise<QdrantClient> {
    if (!this.qdrantClient) {
      this.qdrantClient = await getQdrantClient();
    }
    return this.qdrantClient;
  }

  /**
 * Search for similar vectors in Qdrant collection
 */
async searchSimilar(
  query: string,
  graphName: string = 'mock_hr',
  config: Partial<VectorSimilarityConfig> = {}
): Promise<SearchResult[]> {
    const finalConfig = { ...DEFAULT_VECTOR_CONFIG, ...config };
    const collectionName = getCollectionName(graphName);

    try {
      // Generate query embedding
      console.log(`🔍 Generating query embedding for: "${query.substring(0, 50)}..."`);
      const queryEmbedding = await this.embeddingService.generateQueryEmbedding(
        query,
        finalConfig.dimension
      );

      if (queryEmbedding.length === 0) {
        console.warn('⚠️ Empty query embedding generated');
        return [];
      }

      // Search in Qdrant
      const client = await this.getQdrant();
      
      console.log(`🔍 Searching in collection '${collectionName}' with cosine similarity`);
      
      const searchResult = await client.search(collectionName, {
        vector: queryEmbedding,
        limit: finalConfig.maxResults,
        score_threshold: finalConfig.scoreThreshold,
        with_payload: true,
        with_vector: false // Don't return vectors to save bandwidth
      });

      console.log(`✅ Found ${searchResult.length} similar vectors`);

             // Transform Qdrant results to SearchResult format
       const results: SearchResult[] = searchResult.map((point) => {
         const record = point.payload as unknown as HRSearchRecord;
        
        return {
          record,
          relevance_score: point.score || 0,
          method_contributions: {
            vector_similarity: point.score || 0
          },
          snippets: [] // Vector search doesn't generate text snippets
        };
      });

      return results;

    } catch (error) {
      console.error(`❌ Vector similarity search failed for graph '${graphName}':`, error);
      throw new Error(`Vector similarity search failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
 * Batch index documents with embeddings
 */
async indexDocuments(
  records: HRSearchRecord[],
  graphName: string = 'mock_hr',
  config: Partial<VectorSimilarityConfig> = {}
): Promise<{ indexed: number; errors: string[] }> {
    const finalConfig = { ...DEFAULT_VECTOR_CONFIG, ...config };
    const collectionName = getCollectionName(graphName);
    const errors: string[] = [];
    let indexed = 0;

    try {
      const client = await this.getQdrant();
      
      // Combine text fields for embedding generation
      const texts = records.map(record => {
        const combinedText = [
          record.title,
          record.content,
          record.summary,
          record.fullContent
        ].filter(Boolean).join(' ').trim();
        
        return combinedText || record.title; // Fallback to title if no content
      });

      console.log(`🔄 Generating embeddings for ${texts.length} documents`);
      
      // Generate embeddings in batches
      const embeddings = await this.embeddingService.generateDocumentEmbeddings(
        texts,
        finalConfig.dimension,
        finalConfig.batchSize
      );

      if (embeddings.length !== records.length) {
        throw new Error(`Embedding count mismatch: ${embeddings.length} vs ${records.length}`);
      }

             // Prepare points for Qdrant
       const points = records.map((record, index) => ({
         id: record.id,
         vector: embeddings[index],
         payload: record as unknown as Record<string, unknown>
       }));

      console.log(`📊 Indexing ${points.length} points in collection '${collectionName}'`);

      // Upsert points to Qdrant
      const upsertResult = await client.upsert(collectionName, {
        wait: true,
        points: points
      });

      indexed = points.length;
      console.log(`✅ Successfully indexed ${indexed} documents in collection '${collectionName}'`);

      return { indexed, errors };

    } catch (error) {
      const errorMessage = `Failed to index documents: ${error instanceof Error ? error.message : 'Unknown error'}`;
      console.error(`❌ ${errorMessage}`);
      errors.push(errorMessage);
      
      return { indexed, errors };
    }
  }

  /**
 * Get vector similarity statistics
 */
async getStats(graphName: string = 'mock_hr'): Promise<{
    collection: string;
    totalVectors: number;
    vectorDimension: number;
    indexedAt: string;
  } | null> {
    try {
      const client = await this.getQdrant();
      const collectionName = getCollectionName(graphName);
      
      const collectionInfo = await client.getCollection(collectionName);
      
      if (!collectionInfo) {
        return null;
      }

             return {
         collection: collectionName,
         totalVectors: collectionInfo.points_count || 0,
         vectorDimension: (() => {
           const vectors = collectionInfo.config?.params?.vectors;
           if (typeof vectors === 'object' && vectors && 'size' in vectors) {
             return vectors.size as number;
           }
           return 1024;
         })(),
         indexedAt: new Date().toISOString()
       };

    } catch (error) {
      console.error(`❌ Failed to get vector stats for '${graphName}':`, error);
      return null;
    }
  }
}

/**
 * Utility functions for vector operations
 */
export const VectorUtils = {
  /**
   * Calculate cosine similarity between two vectors
   */
  cosineSimilarity(a: number[], b: number[]): number {
    if (a.length !== b.length) {
      throw new Error('Vectors must have the same length');
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }

    if (normA === 0 || normB === 0) {
      return 0;
    }

    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
  },

  /**
   * Normalize vector to unit length
   */
  normalizeVector(vector: number[]): number[] {
    const norm = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
    if (norm === 0) return vector;
    return vector.map(val => val / norm);
  },

  /**
   * Combine multiple embeddings (average)
   */
  averageEmbeddings(embeddings: number[][]): number[] {
    if (embeddings.length === 0) return [];
    if (embeddings.length === 1) return embeddings[0];

    const dimension = embeddings[0].length;
    const averaged = new Array(dimension).fill(0);

    for (const embedding of embeddings) {
      for (let i = 0; i < dimension; i++) {
        averaged[i] += embedding[i];
      }
    }

    return averaged.map(val => val / embeddings.length);
  }
};

/**
 * Factory function to create vector similarity service
 */
export function createVectorSimilarityService(voyageApiKey?: string): VectorSimilaritySearch {
  return new VectorSimilaritySearch(voyageApiKey);
} 