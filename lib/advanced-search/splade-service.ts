/**
 * BM42 Sparse Vector Service
 * 
 * Implements sparse vector generation using BM42 model via FastEmbed API
 * for advanced search capabilities with named vector support.
 */

export interface SpladeVector {
  indices: number[];
  values: number[];
}

export interface BM42Response {
  vectors: Array<{
    indices: number[];
    values: number[];
  }>;
}

export class SpladeService {
  private bm42Url: string;
  private maxRetries = 3;
  private retryDelay = 1000; // 1 second

  constructor() {
    this.bm42Url = 'https://fastembed-service-501978788717.us-central1.run.app/embed';
  }

  /**
   * Generate sparse vector for a single text using BM42 API
   */
  async generateSparseVector(text: string): Promise<SpladeVector> {
    if (!text?.trim()) {
      return { indices: [], values: [] };
    }

    const result = await this.generateSparseVectors([text]);
    return result[0] || { indices: [], values: [] };
  }

  /**
   * Generate sparse vectors for multiple texts using BM42 API
   */
  async generateSparseVectors(texts: string[], batchSize: number = 50): Promise<SpladeVector[]> {
    if (!texts || texts.length === 0) {
      return [];
    }

    const results: SpladeVector[] = [];
    
    for (let i = 0; i < texts.length; i += batchSize) {
      const batch = texts.slice(i, i + batchSize);
      const batchNumber = Math.floor(i / batchSize) + 1;
      const totalBatches = Math.ceil(texts.length / batchSize);
      
      console.log(`Processing BM42 batch ${batchNumber}/${totalBatches} (${batch.length} texts)`);
      
      // Process batch with BM42 API
      for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
        try {
          const response = await fetch(this.bm42Url, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              texts: batch.map(text => text.trim()).filter(text => text.length > 0)
            })
          });

          if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`BM42 API error (${response.status}): ${errorText}`);
          }

          const data: BM42Response = await response.json();

          // Validate the response format
          if (!data.vectors || !Array.isArray(data.vectors)) {
            throw new Error('Invalid BM42 response format: missing vectors array');
          }

          // BM42 returns sparse vectors directly
          const batchResults = data.vectors.map(vector => ({
            indices: vector.indices || [],
            values: vector.values || []
          }));

          results.push(...batchResults);
          break; // Success, exit retry loop

        } catch (error) {
          console.error(`BM42 generation attempt ${attempt}/${this.maxRetries} failed:`, error);
          
          if (attempt === this.maxRetries) {
            console.error(`Failed to generate BM42 vectors after ${this.maxRetries} attempts for batch ${batchNumber}`);
            // Add empty vectors for failed batch
            const emptyVectors = batch.map(() => ({ indices: [], values: [] }));
            results.push(...emptyVectors);
          } else {
            // Wait before retry
            await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt));
          }
        }
      }
      
      // Add delay between batches to avoid rate limiting
      if (i + batchSize < texts.length) {
        console.log('Waiting 1 second before next batch to avoid rate limits...');
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    const successCount = results.filter(r => r.indices.length > 0).length;
    console.log(`BM42 generation completed: ${successCount}/${texts.length} successful`);

    return results;
  }

  /**
   * Convert sparse vector to Qdrant sparse vector format
   */
  formatForQdrant(sparseVector: SpladeVector) {
    return {
      indices: sparseVector.indices,
      values: sparseVector.values
    };
  }

  /**
   * Test BM42 service connectivity
   */
  async testConnection(): Promise<boolean> {
    try {
      const testResult = await this.generateSparseVector("test connection");
      return testResult.indices.length >= 0; // Even empty result indicates successful connection
    } catch (error) {
      console.error('BM42 service connection test failed:', error);
      return false;
    }
  }

  /**
   * Get service status information
   */
  getStatus() {
    return {
      service: 'BM42 via FastEmbed',
      model: 'BM42',
      endpoint: this.bm42Url,
      configured: true
    };
  }
}

// Export singleton instance
export const spladeService = new SpladeService();

// Export utility functions
export async function generateSingleSpladeVector(text: string): Promise<SpladeVector> {
  return spladeService.generateSparseVector(text);
}

export async function generateBatchSpladeVectors(texts: string[], batchSize?: number): Promise<SpladeVector[]> {
  return spladeService.generateSparseVectors(texts, batchSize);
}

export async function testSpladeService(): Promise<boolean> {
  return spladeService.testConnection();
} 