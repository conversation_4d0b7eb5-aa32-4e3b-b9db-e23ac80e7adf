/**
 * Advanced Search API - Task 3.1: Hybrid Search Engine Implementation
 * 
 * Implements Qdrant Query API with:
 * - Prefetch architecture for multi-stage queries
 * - RRF (Reciprocal Rank Fusion) and DBSF (Distribution-Based Score Fusion)
 * - Multi-stage search pipelines with re-ranking
 * - HR-specific filtering capabilities
 * - Configurable fusion method selection
 */

import { QdrantClient } from '@qdrant/js-client-rest';
import { exactWordMatch, exactPhraseMatch } from '../exact-matching';
import { VoyageEmbeddingService, VectorUtils } from '../vector-similarity';
import { getQdrantClient } from './qdrant-client';
import { SpladeService } from './splade-service';
import type { 
  HRSearchRecord, 
  SearchResult, 
  SearchSnippet,
  AdvancedSearchRequest
} from './types';

// Define HRSpecificFilters type locally since it's not exported from types
export interface HRSpecificFilters {
  section_path?: string[];
  article_type?: 'section' | 'article';
  level?: number;
  update_date_after?: string;
}

// =============================================================================
// TYPES AND INTERFACES
// =============================================================================

export interface FusionMethod {
  type: 'rrf' | 'dbsf' | 'multi_stage' | 'manual_weighted' | 'exact_phrase';
  params?: {
    k?: number; // RRF constant (default: 60 in original paper, 2 in Qdrant)
    alpha?: number; // DBSF alpha parameter for normalization
    vectorWeight?: number; // Manual weighted fusion semantic weight
    sparseWeight?: number; // Manual weighted fusion keyword weight
  };
}

export interface PrefetchConfig {
  query_vector?: number[];
  sparse_vector?: {
    indices: number[];
    values: number[];
  };
  using?: string;
  limit: number;
  filter?: any;
}

export interface HybridSearchConfig {
  // Search method configurations
  search_methods: {
    vector_similarity?: {
      enabled: boolean;
      weight: number;
      model?: 'voyage-3.5';
      dimension?: 256 | 512 | 1024 | 2048;
      using?: string;
    };
    exact_words?: {
      enabled: boolean;
      weight: number;
    };
    exact_phrase?: {
      enabled: boolean;
      weight: number;
    };
    sparse_vectors?: {
      enabled: boolean;
      weight: number;
      using?: string;
    };
  };

  // Fusion configuration
  fusion?: {
    method: FusionMethod;
    enabled: boolean;
  };

  // Multi-stage configuration
  multi_stage?: {
    enabled: boolean;
    stages: {
      prefetch_limit: number;
      using?: string;
      query_type: 'dense' | 'sparse' | 'byte';
    }[];
  };

  // Filtering
  filters?: HRSpecificFilters;

  // Result options
  result_options?: {
    limit?: number;
    offset?: number;
    include_snippets?: boolean;
    snippet_context_words?: number;
  };
}

export interface HybridSearchResult {
  success: boolean;
  query: string;
  collection_name: string;
  total_results: number;
  execution_time_ms: number;
  results: SearchResult[];
  method_scores?: {
    [method: string]: {
      results_count: number;
      avg_score: number;
      execution_time_ms: number;
    };
  };
  fusion_info?: {
    method: FusionMethod;
    combined_results: number;
    unique_results: number;
  };
}

// =============================================================================
// CONSTANTS
// =============================================================================

export const DEFAULT_HR_COLLECTION = 'mock_hr';

// =============================================================================
// DEFAULT CONFIGURATIONS
// =============================================================================

export const DEFAULT_HYBRID_CONFIG: HybridSearchConfig = {
  search_methods: {
    vector_similarity: {
      enabled: true,
      weight: 0.7,
      model: 'voyage-3.5',
      dimension: 1024,
      using: 'dense'
    },
    exact_phrase: {
      enabled: true,
      weight: 0.3
    },
    exact_words: {
      enabled: false,
      weight: 0.2
    },
    sparse_vectors: {
      enabled: true,
      weight: 0.4,
      using: 'sparse'
    }
  },
  fusion: {
    method: { type: 'rrf', params: { k: 2 } },
    enabled: true
  },
  multi_stage: {
    enabled: false,
    stages: []
  },
  result_options: {
    limit: 10,
    offset: 0,
    include_snippets: true,
    snippet_context_words: 10
  }
};

// =============================================================================
// HYBRID SEARCH ENGINE CLASS
// =============================================================================

export class HybridSearchEngine {
  private client: QdrantClient | Promise<QdrantClient>;
  private embeddingService?: VoyageEmbeddingService;

  constructor(client?: QdrantClient) {
    this.client = client || getQdrantClient();
  }

  /**
   * Get the initialized Qdrant client
   */
  private async getClient(): Promise<QdrantClient> {
    if (this.client instanceof Promise) {
      this.client = await this.client;
    }
    return this.client as QdrantClient;
  }

  /**
   * Initialize embedding service if not already done
   */
  private async initializeEmbeddingService(): Promise<void> {
    if (!this.embeddingService) {
      this.embeddingService = new VoyageEmbeddingService();
    }
  }

  /**
   * Generate query embeddings for vector similarity search
   */
  private async generateQueryEmbedding(query: string, config: HybridSearchConfig): Promise<number[]> {
    await this.initializeEmbeddingService();
    
    const vectorConfig = config.search_methods.vector_similarity;
    if (!vectorConfig || !this.embeddingService) {
      throw new Error('Vector similarity not configured or embedding service not available');
    }

    const result = await this.embeddingService.generateEmbeddings([query], {
      model: vectorConfig.model || 'voyage-3.5',
      inputType: 'query',
      dimension: vectorConfig.dimension || 1024
    });

    return result.embeddings[0];
  }

  /**
   * Build Qdrant filter from HR-specific filters
   */
  private buildQdrantFilter(filters?: HRSpecificFilters): any {
    if (!filters) return undefined;

    const conditions: any[] = [];

    // Section path filter
    if (filters.section_path && filters.section_path.length > 0) {
      conditions.push({
        key: 'section_path',
        match: { any: filters.section_path }
      });
    }

    // Article type filter
    if (filters.article_type) {
      conditions.push({
        key: 'article_type',
        match: { value: filters.article_type }
      });
    }

    // Level filter
    if (filters.level !== undefined) {
      conditions.push({
        key: 'level',
        match: { value: filters.level }
      });
    }

    // Date filter
    if (filters.update_date_after) {
      conditions.push({
        key: 'updateDate',
        range: {
          gte: filters.update_date_after
        }
      });
    }

    if (conditions.length === 0) return undefined;
    if (conditions.length === 1) return { must: conditions };
    return { must: conditions };
  }

  /**
   * Perform basic hybrid search using RRF fusion
   */
  async basicHybridSearch(
    query: string,
    collection_name: string,
    config: HybridSearchConfig = DEFAULT_HYBRID_CONFIG
  ): Promise<HybridSearchResult> {
    const start_time = Date.now();
    
    try {
      const prefetches: any[] = [];
      const filter = this.buildQdrantFilter(config.filters);
      const limit = config.result_options?.limit || 10;

      // Vector similarity prefetch
      if (config.search_methods.vector_similarity?.enabled) {
        const queryEmbedding = await this.generateQueryEmbedding(query, config);
        const vectorLimit = Math.max(limit * 4, 20); // Prefetch more for better fusion
        
        prefetches.push({
          query: queryEmbedding,
          using: 'dense', // Use named vector
          limit: vectorLimit,
          filter: filter
        });
      }

      // Sparse vector prefetch (if enabled and available)
      if (config.search_methods.sparse_vectors?.enabled) {
        try {
          const spladeService = new SpladeService();
          const sparseVector = await spladeService.generateSparseVector(query);
          
          if (sparseVector.indices.length > 0) {
            const sparseLimit = Math.max(limit * 4, 20);
            prefetches.push({
              query: sparseVector,
              using: 'sparse',
              limit: sparseLimit,
              filter: filter
            });
            console.log(`✅ Generated sparse vector with ${sparseVector.indices.length} non-zero values`);
          } else {
            console.warn('⚠️ BM42 service generated empty sparse vector');
          }
        } catch (error) {
          console.error('❌ Sparse vector generation failed:', error);
          console.warn('Sparse vector search not available - continuing with dense vectors only');
        }
      }

      // Check if we need manual weighted fusion for true score-level weighting
      const vectorWeight = config.search_methods.vector_similarity?.weight || 0.6;
      const sparseWeight = config.search_methods.sparse_vectors?.weight || 0.4;
      const weightDifference = Math.abs(vectorWeight - sparseWeight);
      
      // Use manual weighted fusion when weights differ significantly
      // This gives us true point-level semantic vs keyword weighting
      if (weightDifference > 0.1 && prefetches.length > 1) {
        console.log(`🎛️ Using manual weighted fusion (Semantic: ${vectorWeight}, Keyword: ${sparseWeight})`);
        return this.performManualWeightedFusion(prefetches, config, limit, filter, collection_name, query);
      }
      
      // For balanced weights, use Qdrant's built-in fusion
      const fusion_method = 'rrf'; // RRF works fine for balanced weights
      console.log(`🔀 Using ${fusion_method.toUpperCase()} fusion (balanced weights)`);
      
      const searchParams: any = {
        prefetch: prefetches,
        query: { fusion: fusion_method as 'rrf' | 'dbsf' },
        limit,
        with_payload: true,
        with_vector: false
      };

      if (config.result_options?.offset) {
        searchParams.offset = config.result_options.offset;
      }

      if (filter) {
        searchParams.filter = filter;
      }

      // Execute Qdrant hybrid search
      const client = await this.getClient();
      const qdrantResponse = await client.query(collection_name, searchParams);
      
      // Transform Qdrant results to our format
      const searchResults: SearchResult[] = qdrantResponse.points.map((point: any) => ({
        record: {
          id: point.id.toString(),
          title: point.payload.title || '',
          content: point.payload.content || point.payload.summary || '',
          summary: point.payload.summary || '',
          fullContent: point.payload.fullContent || point.payload.content || '',
          updateDate: point.payload.updateDate || '',
          breadcrumb: point.payload.breadcrumb || '',
          section_path: point.payload.section_path || [],
          article_type: point.payload.article_type || 'article',
          level: point.payload.level || 0,
          nodeType: point.payload.nodeType || 'record',
          country: point.payload.country || '',
          htmlUrl: point.payload.htmlUrl || '',
          sectionId: point.payload.sectionId,
          recordId: point.payload.recordId
        },
        relevance_score: point.score || 0,
        method_contributions: {
          'qdrant_hybrid': point.score || 0
        },
        snippets: config.result_options?.include_snippets ? 
          this.extractSnippets(query, point.payload.content || point.payload.summary || '', config) : undefined
      }));

      const execution_time = Date.now() - start_time;

      return {
        success: true,
        query,
        collection_name,
        total_results: searchResults.length,
        execution_time_ms: execution_time,
        results: searchResults,
        fusion_info: {
          method: config.fusion?.method || { type: 'rrf' },
          combined_results: prefetches.length,
          unique_results: searchResults.length
        }
      };

    } catch (error) {
      console.error('Hybrid search error:', error);
      
      return {
        success: false,
        query,
        collection_name,
        total_results: 0,
        execution_time_ms: Date.now() - start_time,
        results: [],
        method_scores: {
          error: {
            results_count: 0,
            avg_score: 0,
            execution_time_ms: Date.now() - start_time
          }
        }
      };
    }
  }

  /**
   * Perform manual weighted fusion with true score-level weighting
   * This gives us accurate 90% semantic / 10% keyword weighting at the point level
   */
  private async performManualWeightedFusion(
    prefetches: any[],
    config: HybridSearchConfig,
    limit: number,
    filter: any,
    collection_name: string,
    query: string
  ): Promise<HybridSearchResult> {
    const start_time = Date.now();
    
    try {
      const client = await this.getClient();
      const vectorWeight = config.search_methods.vector_similarity?.weight || 0.6;
      const sparseWeight = config.search_methods.sparse_vectors?.weight || 0.4;
      
      // Execute each search method separately to get individual scores
      const methodResults: { [method: string]: any[] } = {};
      
      for (const prefetch of prefetches) {
        const searchParams: any = {
          limit: Math.max(limit * 3, 30), // Get more results for better fusion
          with_payload: true,
          with_vector: false
        };
        
        // Handle different vector types properly - use 'query' parameter instead of 'vector'
        if (prefetch.using === 'sparse') {
          // For sparse vector search, use the correct format
          searchParams.query = {
            indices: prefetch.query.indices || [],
            values: prefetch.query.values || []
          };
          searchParams.using = 'sparse'; // Specify the named vector
        } else {
          // For dense vector search
          searchParams.query = prefetch.query; // Dense vector (default)
          searchParams.using = 'dense'; // Specify the named vector for dense search
        }
        
        if (filter) {
          searchParams.filter = filter;
        }
        
        const response = await client.query(collection_name, searchParams);
        const methodName = prefetch.using === 'dense' ? 'semantic' : 'keyword';
        
        // Handle response structure correctly - points should be in response.points
        methodResults[methodName] = response.points || [];
        
        console.log(`  ✅ ${methodName}: ${methodResults[methodName].length} results`);
      }
      
      // Perform score-level weighted fusion
      const fusedResults = this.performScoreLevelWeightedFusion(
        methodResults,
        { semantic: vectorWeight, keyword: sparseWeight },
        limit
      );
      
      // Transform to our result format
      const searchResults: SearchResult[] = fusedResults.map((point: any) => ({
        record: {
          id: point.id.toString(),
          title: point.payload.title || '',
          content: point.payload.content || point.payload.summary || '',
          summary: point.payload.summary || '',
          fullContent: point.payload.fullContent || point.payload.content || '',
          updateDate: point.payload.updateDate || '',
          breadcrumb: point.payload.breadcrumb || '',
          section_path: point.payload.section_path || [],
          article_type: point.payload.article_type || 'article',
          level: point.payload.level || 0,
          nodeType: point.payload.nodeType || 'record',
          country: point.payload.country || '',
          htmlUrl: point.payload.htmlUrl || '',
          sectionId: point.payload.sectionId,
          recordId: point.payload.recordId
        },
        relevance_score: point.weightedScore,
        method_contributions: {
          semantic: point.semanticScore || 0,
          keyword: point.keywordScore || 0,
          weighted_fusion: point.weightedScore
        },
        snippets: config.result_options?.include_snippets ? 
          this.extractSnippets(query, point.payload.content || point.payload.summary || '', config) : undefined
      }));

      const execution_time = Date.now() - start_time;

      return {
        success: true,
        query,
        collection_name,
        total_results: searchResults.length,
        execution_time_ms: execution_time,
        results: searchResults,
        fusion_info: {
          method: { type: 'manual_weighted', params: { vectorWeight, sparseWeight } },
          combined_results: Object.values(methodResults).reduce((sum, results) => sum + results.length, 0),
          unique_results: searchResults.length
        }
      };

    } catch (error) {
      console.error('❌ Manual weighted fusion failed:', error);
      
      return {
        success: false,
        query,
        collection_name,
        total_results: 0,
        execution_time_ms: Date.now() - start_time,
        results: [],
        method_scores: {
          error: {
            results_count: 0,
            avg_score: 0,
            execution_time_ms: Date.now() - start_time
          }
        }
      };
    }
  }

  /**
   * Perform score-level weighted fusion with proper normalization
   */
  private performScoreLevelWeightedFusion(
    methodResults: { [method: string]: any[] },
    weights: { [method: string]: number },
    limit: number
  ): any[] {
    // Normalize weights to sum to 1
    const totalWeight = Object.values(weights).reduce((sum, w) => sum + w, 0);
    const normalizedWeights = Object.fromEntries(
      Object.entries(weights).map(([method, weight]) => [method, weight / totalWeight])
    );
    
    console.log(`  📊 Normalized weights:`, normalizedWeights);
    
    // Create a map of all unique points with their scores from each method
    const pointScores: { [pointId: string]: { 
      point: any, 
      scores: { [method: string]: number },
      normalizedScores: { [method: string]: number }
    } } = {};
    
    // Collect all scores and normalize each method separately
    for (const [method, results] of Object.entries(methodResults)) {
      if (!results.length) continue;
      
      // Extract raw scores
      const rawScores = results.map(p => p.score || 0);
      
      // Normalize using min-max normalization (0 to 1)
      const minScore = Math.min(...rawScores);
      const maxScore = Math.max(...rawScores);
      const scoreRange = maxScore - minScore || 1; // Avoid division by zero
      
      console.log(`  📈 ${method}: raw scores ${minScore.toFixed(3)} to ${maxScore.toFixed(3)}`);
      
      results.forEach((point, index) => {
        const pointId = point.id.toString();
        const rawScore = rawScores[index];
        const normalizedScore = (rawScore - minScore) / scoreRange;
        
        if (!pointScores[pointId]) {
          pointScores[pointId] = {
            point: point,
            scores: {},
            normalizedScores: {}
          };
        }
        
        pointScores[pointId].scores[method] = rawScore;
        pointScores[pointId].normalizedScores[method] = normalizedScore;
      });
    }
    
    // Calculate weighted scores for each point
    const weightedResults = Object.values(pointScores).map(({ point, scores, normalizedScores }) => {
      let weightedScore = 0;
      
      for (const [method, weight] of Object.entries(normalizedWeights)) {
        const normalizedScore = normalizedScores[method] || 0;
        weightedScore += normalizedScore * weight;
      }
      
      return {
        ...point,
        semanticScore: normalizedScores.semantic || 0,
        keywordScore: normalizedScores.keyword || 0,
        weightedScore: weightedScore
      };
    });
    
    // Sort by weighted score and return top results
    return weightedResults
      .sort((a, b) => b.weightedScore - a.weightedScore)
      .slice(0, limit);
  }

  /**
   * Perform multi-stage hybrid search with re-ranking
   */
  async multiStageHybridSearch(
    query: string,
    collection_name: string,
    config: HybridSearchConfig
  ): Promise<HybridSearchResult> {
    const start_time = Date.now();
    
    try {
      if (!config.multi_stage?.enabled || !config.multi_stage.stages.length) {
        return this.basicHybridSearch(query, collection_name, config);
      }

      const stages = config.multi_stage.stages;
      const queryEmbedding = await this.generateQueryEmbedding(query, config);
      const filter = this.buildQdrantFilter(config.filters);

      // Build nested prefetch structure for multi-stage search
      let currentPrefetch: any = {
        query: queryEmbedding,
        using: stages[0].using || 'dense',
        limit: stages[0].prefetch_limit || 1000,
        filter: filter
      };

      // Build nested prefetches for each stage
      for (let i = 1; i < stages.length; i++) {
        currentPrefetch = {
          prefetch: currentPrefetch,
          query: queryEmbedding,
          using: stages[i].using || 'dense',
          limit: stages[i].prefetch_limit || 100
        };
      }

      // Final query
      const searchParams: any = {
        prefetch: currentPrefetch,
        query: queryEmbedding,
        using: 'dense', // Final re-ranking with dense vectors
        limit: config.result_options?.limit || 10,
        with_payload: true,
        with_vector: false
      };

      if (config.result_options?.offset) {
        searchParams.offset = config.result_options.offset;
      }

      const client = await this.getClient();
      const qdrantResponse = await client.query(collection_name, searchParams);

      // Transform results
      const searchResults: SearchResult[] = qdrantResponse.points.map((point: any) => ({
        record: {
          id: point.id.toString(),
          title: point.payload.title || '',
          content: point.payload.content || point.payload.summary || '',
          summary: point.payload.summary || '',
          fullContent: point.payload.fullContent || point.payload.content || '',
          updateDate: point.payload.updateDate || '',
          breadcrumb: point.payload.breadcrumb || '',
          section_path: point.payload.section_path || [],
          article_type: point.payload.article_type || 'article',
          level: point.payload.level || 0,
          nodeType: point.payload.nodeType || 'record',
          country: point.payload.country || '',
          htmlUrl: point.payload.htmlUrl || '',
          sectionId: point.payload.sectionId,
          recordId: point.payload.recordId
        },
        relevance_score: point.score || 0,
        method_contributions: {
          'multi_stage_hybrid': point.score || 0
        },
        snippets: config.result_options?.include_snippets ? 
          this.extractSnippets(query, point.payload.content || point.payload.summary || '', config) : undefined
      }));

      const execution_time = Date.now() - start_time;

      return {
        success: true,
        query,
        collection_name,
        total_results: searchResults.length,
        execution_time_ms: execution_time,
        results: searchResults,
        fusion_info: {
          method: { type: 'multi_stage' },
          combined_results: stages.length,
          unique_results: searchResults.length
        }
      };

    } catch (error) {
      console.error('Multi-stage hybrid search error:', error);
      
      return {
        success: false,
        query,
        collection_name,
        total_results: 0,
        execution_time_ms: Date.now() - start_time,
        results: []
      };
    }
  }

  /**
   * Perform advanced hybrid search with DBSF fusion
   */
  async advancedHybridSearch(
    query: string,
    collection_name: string,
    config: HybridSearchConfig
  ): Promise<HybridSearchResult> {
    const start_time = Date.now();
    
    try {
      // For DBSF fusion, we'll use the same structure as RRF but specify DBSF
      const modifiedConfig = {
        ...config,
        fusion: {
          method: { type: 'dbsf' as const },
          enabled: true
        }
      };

      const prefetches: any[] = [];
      const filter = this.buildQdrantFilter(config.filters);
      const limit = config.result_options?.limit || 10;

      // Vector similarity prefetch with proper using parameter
      if (config.search_methods.vector_similarity?.enabled) {
        const queryEmbedding = await this.generateQueryEmbedding(query, config);
        const vectorLimit = Math.max(limit * 4, 20);
        
        prefetches.push({
          query: queryEmbedding,
          using: 'dense', // Specify named vector
          limit: vectorLimit,
          filter: filter
        });
      }

      // Add sparse vector prefetch for DBSF if enabled
      if (config.search_methods.sparse_vectors?.enabled) {
        try {
          const spladeService = new SpladeService();
          const sparseVector = await spladeService.generateSparseVector(query);
          
          if (sparseVector.indices.length > 0) {
            const sparseLimit = Math.max(limit * 4, 20);
            prefetches.push({
              query: sparseVector,
              using: 'sparse',
              limit: sparseLimit,
              filter: filter
            });
          }
        } catch (error) {
          console.warn('DBSF: Sparse vector generation failed, continuing with dense only');
        }
      }

      // Execute with DBSF fusion - use proper query structure
      const searchParams: any = {
        prefetch: prefetches,
        query: { fusion: 'dbsf' }, // Use DBSF fusion method
        limit,
        with_payload: true,
        with_vector: false
      };

      if (config.result_options?.offset) {
        searchParams.offset = config.result_options.offset;
      }

      if (filter) {
        searchParams.filter = filter;
      }

      const client = await this.getClient();
      const qdrantResponse = await client.query(collection_name, searchParams);

      // Transform results
      const searchResults: SearchResult[] = qdrantResponse.points.map((point: any) => ({
        record: {
          id: point.id.toString(),
          title: point.payload.title || '',
          content: point.payload.content || point.payload.summary || '',
          summary: point.payload.summary || '',
          fullContent: point.payload.fullContent || point.payload.content || '',
          updateDate: point.payload.updateDate || '',
          breadcrumb: point.payload.breadcrumb || '',
          section_path: point.payload.section_path || [],
          article_type: point.payload.article_type || 'article',
          level: point.payload.level || 0,
          nodeType: point.payload.nodeType || 'record',
          country: point.payload.country || '',
          htmlUrl: point.payload.htmlUrl || '',
          sectionId: point.payload.sectionId,
          recordId: point.payload.recordId
        },
        relevance_score: point.score || 0,
        method_contributions: {
          'qdrant_dbsf': point.score || 0
        },
        snippets: config.result_options?.include_snippets ? 
          this.extractSnippets(query, point.payload.content || point.payload.summary || '', config) : undefined
      }));

      const execution_time = Date.now() - start_time;

      return {
        success: true,
        query,
        collection_name,
        total_results: searchResults.length,
        execution_time_ms: execution_time,
        results: searchResults,
        fusion_info: {
          method: { type: 'dbsf' },
          combined_results: prefetches.length,
          unique_results: searchResults.length
        }
      };

    } catch (error) {
      console.error('Advanced hybrid search error:', error);
      
      return {
        success: false,
        query,
        collection_name,
        total_results: 0,
        execution_time_ms: Date.now() - start_time,
        results: []
      };
    }
  }

  /**
   * Perform fallback hybrid search using manual fusion when Qdrant methods fail
   */
  async fallbackHybridSearch(
    query: string,
    records: HRSearchRecord[],
    config: HybridSearchConfig
  ): Promise<HybridSearchResult> {
    const start_time = Date.now();
    
    try {
      const methodResults: { [method: string]: SearchResult[] } = {};
      const methodTimes: { [method: string]: number } = {};

      // Exact word matching
      if (config.search_methods.exact_words?.enabled) {
        const wordStart = Date.now();
        methodResults.exact_words = exactWordMatch(query, records);
        methodTimes.exact_words = Date.now() - wordStart;
      }

      // Exact phrase matching
      if (config.search_methods.exact_phrase?.enabled) {
        const phraseStart = Date.now();
        methodResults.exact_phrase = exactPhraseMatch(query, records);
        methodTimes.exact_phrase = Date.now() - phraseStart;
      }

      // Vector similarity (if we have embeddings)
      if (config.search_methods.vector_similarity?.enabled && this.embeddingService) {
        const vectorStart = Date.now();
        // This would require vector similarity search on the records
        // For now, we'll skip this as it requires the records to have embeddings
        methodTimes.vector_similarity = Date.now() - vectorStart;
      }

      // Manual fusion using weighted combination
      const fusedResults = this.manualFusion(methodResults, config);

      const execution_time = Date.now() - start_time;

      return {
        success: true,
        query,
        collection_name: 'fallback',
        total_results: fusedResults.length,
        execution_time_ms: execution_time,
        results: fusedResults.slice(0, config.result_options?.limit || 10),
        method_scores: Object.entries(methodResults).reduce((acc, [method, results]) => {
          acc[method] = {
            results_count: results.length,
            avg_score: results.reduce((sum, r) => sum + r.relevance_score, 0) / results.length || 0,
            execution_time_ms: methodTimes[method] || 0
          };
          return acc;
        }, {} as any)
      };

    } catch (error) {
      console.error('Fallback hybrid search error:', error);
      
      return {
        success: false,
        query,
        collection_name: 'fallback',
        total_results: 0,
        execution_time_ms: Date.now() - start_time,
        results: []
      };
    }
  }

  /**
   * Manual fusion of results from different search methods
   */
  private manualFusion(
    methodResults: { [method: string]: SearchResult[] },
    config: HybridSearchConfig
  ): SearchResult[] {
    const recordScores: { [recordId: string]: {
      record: HRSearchRecord;
      totalScore: number;
      methodContributions: { [method: string]: number };
      snippets?: SearchSnippet[];
    } } = {};

    // Combine results from all methods
    for (const [method, results] of Object.entries(methodResults)) {
      const weight = this.getMethodWeight(method, config);
      
      for (const result of results) {
        const recordId = result.record.id;
        
        if (!recordScores[recordId]) {
          recordScores[recordId] = {
            record: result.record,
            totalScore: 0,
            methodContributions: {},
            snippets: result.snippets
          };
        }
        
        const weightedScore = result.relevance_score * weight;
        recordScores[recordId].totalScore += weightedScore;
        recordScores[recordId].methodContributions[method] = weightedScore;
        
        // Combine snippets if available
        if (result.snippets && result.snippets.length > 0) {
          if (!recordScores[recordId].snippets) {
            recordScores[recordId].snippets = [];
          }
          recordScores[recordId].snippets.push(...result.snippets);
        }
      }
    }

    // Convert to SearchResult array and sort by total score
    return Object.values(recordScores)
      .map(item => ({
        record: item.record,
        relevance_score: item.totalScore,
        method_contributions: item.methodContributions,
        snippets: item.snippets
      }))
      .sort((a, b) => b.relevance_score - a.relevance_score);
  }

  /**
   * Get weight for a specific search method
   */
  private getMethodWeight(method: string, config: HybridSearchConfig): number {
    switch (method) {
      case 'exact_words':
        return config.search_methods.exact_words?.weight || 0.2;
      case 'exact_phrase':
        return config.search_methods.exact_phrase?.weight || 0.3;
      case 'vector_similarity':
        return config.search_methods.vector_similarity?.weight || 0.7;
      case 'sparse_vectors':
        return config.search_methods.sparse_vectors?.weight || 0.4;
      default:
        return 1.0;
    }
  }

  /**
   * Extract snippets from content for a given query
   */
  private extractSnippets(
    query: string,
    content: string,
    config: HybridSearchConfig
  ): SearchSnippet[] {
    if (!config.result_options?.include_snippets || !content) {
      return [];
    }

    const contextWords = config.result_options.snippet_context_words || 10;
    const queryTerms = query.toLowerCase().split(/\s+/);
    const contentLower = content.toLowerCase();
    const words = content.split(/\s+/);
    const snippets: SearchSnippet[] = [];

    for (const term of queryTerms) {
      const termIndex = contentLower.indexOf(term.toLowerCase());
      if (termIndex !== -1) {
        // Find word boundaries
        const beforeWords = content.substring(0, termIndex).split(/\s+/);
        const startWordIndex = Math.max(0, beforeWords.length - contextWords);
        const endWordIndex = Math.min(words.length, beforeWords.length + contextWords);
        
        const snippetWords = words.slice(startWordIndex, endWordIndex);
        const snippetText = snippetWords.join(' ');
        
        const termStartInSnippet = snippetText.toLowerCase().indexOf(term.toLowerCase());
        
        if (termStartInSnippet !== -1) {
          snippets.push({
            text: term,
            context_before: snippetText.substring(0, termStartInSnippet).trim(),
            context_after: snippetText.substring(termStartInSnippet + term.length).trim(),
            start_position: termIndex,
            end_position: termIndex + term.length,
            relevance_score: 1.0
          });
        }
      }
    }

    return snippets;
  }

  /**
   * Exact phrase matching using Qdrant's full-text search
   * Supports field-specific searches across nodes (title, content) and articles (title, summary, fullContent)
   */
  async exactPhraseMatch(
    phrase: string,
    collection_name: string,
    options: {
      limit?: number;
      search_fields?: ('title' | 'content' | 'summary' | 'fullContent')[];
      entity_types?: ('node' | 'article' | 'both')[];
      include_snippets?: boolean;
      snippet_context_words?: number;
      case_sensitive?: boolean;
      whole_words_only?: boolean;
    } = {}
  ): Promise<HybridSearchResult> {
    const start_time = Date.now();
    
    const {
      limit = 10,
      search_fields = ['title', 'content', 'summary', 'fullContent'],
      entity_types = ['both'],
      include_snippets = true,
      snippet_context_words = 15,
      case_sensitive = false,
      whole_words_only = false
    } = options;

    try {
      console.log(`🔍 Performing exact phrase search for: "${phrase}"`);
      console.log(`📚 Collection: ${collection_name}`);
      console.log(`🎯 Search fields: ${search_fields.join(', ')}`);
      console.log(`📊 Entity types: ${entity_types.join(', ')}`);
      console.log(`🔤 Case sensitive: ${case_sensitive}`);
      console.log(`🔤 Whole words only: ${whole_words_only}`);

      const client = await getQdrantClient();
      
      // Prepare search phrase based on options
      let searchPhrase = phrase;
      if (!case_sensitive) {
        searchPhrase = phrase.toLowerCase();
      }
      if (whole_words_only) {
        searchPhrase = `\\b${searchPhrase}\\b`; // Word boundary regex
      }
      
      // Build search conditions for each field
      const searchConditions = search_fields.map(field => ({
        key: field,
        match: {
          text: `"${searchPhrase}"` // Quoted phrase for exact matching
        }
      }));

      // Build entity type filter conditions
      const entityConditions: any[] = [];
      if (!entity_types.includes('both')) {
        if (entity_types.includes('node')) {
          entityConditions.push({
            key: 'nodeType',
            match: { value: 'category' } // Nodes are stored as 'category' type
          });
        }
        if (entity_types.includes('article')) {
          entityConditions.push({
            key: 'nodeType', 
            match: { value: 'record' } // Articles are stored as 'record' type
          });
        }
      }

      // Combine search and entity filters
      const filter: any = {
        should: searchConditions
      };

      // Add entity type filter if specified
      if (entityConditions.length > 0) {
        filter.must = entityConditions.length === 1 ? 
          entityConditions[0] : 
          { should: entityConditions };
      }

      // Use Qdrant's text filtering - trust the database to do its job
      const searchResponse = await client.scroll(collection_name, {
        limit: limit * 2, // Get some extra results for scoring flexibility
        filter: filter, // ✅ ACTUALLY USE THE FILTER WE BUILT
        with_payload: true,
        with_vector: false
      });

      const points = searchResponse.points || [];
      console.log(`📊 Qdrant filtered results: ${points.length}`);

      // Transform Qdrant results directly - no redundant verification needed
      const searchResults = points
        .map((point: any, index: number) => {
          const payload = point.payload;
          
          // Calculate simple relevance score based on result position (Qdrant already filtered for us)
          const positionScore = Math.max(0.1, (points.length - index) / points.length);
          const relevance_score = positionScore;

          // Extract snippets with exact phrase highlighted
          const searchableText = search_fields
            .map(field => payload[field] || '')
            .join(' ');
            
          const snippets = include_snippets ? 
            this.extractExactPhraseSnippets(phrase, searchableText, snippet_context_words) : 
            undefined;

          return {
            record: {
              id: point.id.toString(),
              title: payload.title || '',
              content: payload.content || payload.summary || '',
              summary: payload.summary || '',
              fullContent: payload.fullContent || payload.content || '',
              updateDate: payload.updateDate || '',
              breadcrumb: payload.breadcrumb || '',
              section_path: payload.section_path || [],
              article_type: payload.article_type || 'article',
              level: payload.level || 0,
              nodeType: payload.nodeType || 'record',
              country: payload.country || '',
              htmlUrl: payload.htmlUrl || '',
              sectionId: payload.sectionId,
              recordId: payload.recordId
            },
            relevance_score,
            method_contributions: {
              'exact_phrase': relevance_score
            },
            snippets
          };
        })
        .slice(0, limit);

      const execution_time = Date.now() - start_time;

      console.log(`✅ Exact phrase search completed: ${searchResults.length} Qdrant matches`);
      console.log(`⏱️ Execution time: ${execution_time}ms`);

      return {
        success: true,
        query: phrase,
        collection_name,
        total_results: searchResults.length,
        execution_time_ms: execution_time,
        results: searchResults,
        fusion_info: {
          method: { type: 'exact_phrase' },
          combined_results: 1,
          unique_results: searchResults.length
        }
      };

    } catch (error) {
      console.error('❌ Exact phrase search failed:', error);
      
      return {
        success: false,
        query: phrase,
        collection_name,
        total_results: 0,
        execution_time_ms: Date.now() - start_time,
        results: []
      };
    }
  }

  /**
   * Extract snippets containing the exact phrase with context
   */
  private extractExactPhraseSnippets(
    phrase: string,
    text: string,
    contextWords: number = 15
  ): SearchSnippet[] {
    const lowerPhrase = phrase.toLowerCase();
    const lowerText = text.toLowerCase();
    const snippets: SearchSnippet[] = [];
    
    let searchStart = 0;
    let matchIndex;
    
    // Find all occurrences of the phrase
    while ((matchIndex = lowerText.indexOf(lowerPhrase, searchStart)) !== -1) {
      // Find word boundaries around the match
      const words = text.split(/\s+/);
      let phraseWordStart = 0;
      let currentPos = 0;
      
      // Find which word the phrase starts in
      for (let i = 0; i < words.length; i++) {
        if (currentPos >= matchIndex) {
          phraseWordStart = Math.max(0, i - contextWords);
          break;
        }
        currentPos += words[i].length + 1; // +1 for space
      }
      
      // Extract context around the phrase
      const snippetWords = words.slice(phraseWordStart, phraseWordStart + (contextWords * 2) + 5);
      const snippetText = snippetWords.join(' ');
      
      // Only add if it contains the phrase and isn't duplicate
      if (snippetText.toLowerCase().includes(lowerPhrase) && 
          !snippets.some(s => s.text === snippetText)) {
        
        // Find the exact position of the phrase within the snippet
        const snippetLower = snippetText.toLowerCase();
        const phraseStartInSnippet = snippetLower.indexOf(lowerPhrase);
        const phraseEndInSnippet = phraseStartInSnippet + phrase.length;
        
        const snippet: SearchSnippet = {
          text: snippetText,
          context_before: snippetText.substring(0, phraseStartInSnippet),
          context_after: snippetText.substring(phraseEndInSnippet),
          start_position: matchIndex,
          end_position: matchIndex + phrase.length,
          relevance_score: 1.0 // Perfect match for exact phrase
        };
        
        snippets.push(snippet);
      }
      
      // Move search position forward
      searchStart = matchIndex + phrase.length;
      
      // Limit to 3 snippets per result
      if (snippets.length >= 3) break;
    }
    
    return snippets;
  }
}

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

/**
 * Create a hybrid search engine instance
 */
export function createHybridSearchEngine(client?: QdrantClient): HybridSearchEngine {
  return new HybridSearchEngine(client);
}

/**
 * Quick hybrid search with default configuration
 */
export async function quickHybridSearch(
  query: string,
  collection_name: string = DEFAULT_HR_COLLECTION,
  config: Partial<HybridSearchConfig> = {}
): Promise<HybridSearchResult> {
  const engine = createHybridSearchEngine();
  const fullConfig = { ...DEFAULT_HYBRID_CONFIG, ...config };
  
  return engine.basicHybridSearch(query, collection_name, fullConfig);
}

/**
 * HR-optimized hybrid search with sensible defaults
 */
export async function hrHybridSearch(
  query: string,
  collection_name: string = DEFAULT_HR_COLLECTION,
  filters?: HRSpecificFilters,
  limit: number = 10
): Promise<HybridSearchResult> {
  const config: HybridSearchConfig = {
    ...DEFAULT_HYBRID_CONFIG,
    search_methods: {
      vector_similarity: {
        enabled: true,
        weight: 0.6,
        model: 'voyage-3.5',
        dimension: 1024
      },
      exact_phrase: {
        enabled: true,
        weight: 0.4
      }
    },
    filters,
    result_options: {
      limit,
      include_snippets: true,
      snippet_context_words: 15
    }
  };

  const engine = createHybridSearchEngine();
  return engine.basicHybridSearch(query, collection_name, config);
}

/**
 * Quick exact phrase search for nodes (categories)
 * Searches in title and content fields only
 */
export async function exactPhraseSearchNodes(
  phrase: string,
  collection_name: string = DEFAULT_HR_COLLECTION,
  limit: number = 10
): Promise<HybridSearchResult> {
  const engine = createHybridSearchEngine();
  
  return engine.exactPhraseMatch(phrase, collection_name, {
    search_fields: ['title', 'content'],
    entity_types: ['node'],
    limit,
    include_snippets: true,
    snippet_context_words: 15
  });
}

/**
 * Quick exact phrase search for articles
 * Searches in title, summary, and fullContent fields
 */
export async function exactPhraseSearchArticles(
  phrase: string,
  collection_name: string = DEFAULT_HR_COLLECTION,
  limit: number = 10
): Promise<HybridSearchResult> {
  const engine = createHybridSearchEngine();
  
  return engine.exactPhraseMatch(phrase, collection_name, {
    search_fields: ['title', 'summary', 'fullContent'],
    entity_types: ['article'],
    limit,
    include_snippets: true,
    snippet_context_words: 15
  });
}

/**
 * Quick exact phrase search across all content
 * Searches all fields for both nodes and articles
 */
export async function exactPhraseSearchAll(
  phrase: string,
  collection_name: string = DEFAULT_HR_COLLECTION,
  options: {
    limit?: number;
    case_sensitive?: boolean;
    include_snippets?: boolean;
  } = {}
): Promise<HybridSearchResult> {
  const engine = createHybridSearchEngine();
  
  const {
    limit = 10,
    case_sensitive = false,
    include_snippets = true
  } = options;
  
  return engine.exactPhraseMatch(phrase, collection_name, {
    search_fields: ['title', 'content', 'summary', 'fullContent'],
    entity_types: ['both'],
    limit,
    case_sensitive,
    include_snippets,
    snippet_context_words: 15
  });
}

export default HybridSearchEngine; 