import { syncFalkorToQdrant } from '@/lib/advanced-search/sync-service';

/**
 * Manual sync trigger for FalkorDB to Qdrant
 */
async function triggerSync() {
  try {
    console.log('🔄 Starting manual sync trigger...');
    
    const result = await syncFalkorToQdrant('mock_hr', {
      apiBaseUrl: 'http://localhost:3000',
      batchSize: 500,
      forceRecreate: true
    });

    console.log('📊 Sync Results:');
    console.log(`   Success: ${result.success}`);
    console.log(`   Processed: ${result.totalProcessed}`);
    console.log(`   Synced: ${result.totalSynced}`);
    console.log(`   Duration: ${result.duration}ms`);
    console.log(`   Message: ${result.message}`);

    if (result.errors.length > 0) {
      console.log(`   Errors: ${result.errors.length}`);
      result.errors.forEach((error, index) => {
        console.log(`     ${index + 1}. ${error}`);
      });
    }

    if (result.success) {
      console.log('✅ Sync completed successfully!');
    } else {
      console.error('❌ Sync failed!');
      process.exit(1);
    }

  } catch (error) {
    console.error('💥 Sync trigger failed:', error);
    process.exit(1);
  }
}

// Run the sync if this file is executed directly
if (require.main === module) {
  triggerSync();
}

export { triggerSync };