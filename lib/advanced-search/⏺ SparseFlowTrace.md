⏺ Sparse Vector Embeddings Flow Trace

Here's the complete function flow for sparse vector embeddings after a search
API request:

1. API Entry Point

/app/api/search/advanced/route.ts:28 - P<PERSON>T handler receives search request

2. Search Engine Routing

/app/api/search/advanced/route.ts:81-82 - Initializes HybridSearchEngine

- Routes to sparse vector search when search_methods.sparse_vectors.enabled =
  true
- Or when single_vector_mode.vector_type = 'sparse'

3. Sparse Vector Generation

/lib/advanced-search/splade-service.ts:32 - generateSparseVector() method

- BM42 API Call: https://fastembed-service-************.us-central1.run.app/embed
- Model: BM42
- Payload: { texts: [text] }
- Authentication: None required

4. Response Processing

/lib/advanced-search/splade-service.ts:76-87 - Processes BM42 response format

- BM42 returns sparse vectors directly
- Response format: { vectors: [{ indices: number[], values: number[] }] }
- Returns { indices: number[], values: number[] }

5. Qdrant Integration

/lib/advanced-search/splade-service.ts:123 - formatForQdrant() method

- Formats sparse vector for Qdrant storage/search

Key Components Updated from RunPod to BM42:

1. API Endpoint (splade-service.ts:26):
   - OLD: https://api.runpod.ai/v2/${ENDPOINT_ID}/runsync
   - NEW: https://fastembed-service-************.us-central1.run.app/embed

2. Request Format (splade-service.ts:61-69):
   - OLD: Headers: Authorization: Bearer ${API_KEY}
   - OLD: Body: { input: { model: "naver/splade-v3", input: text } }
   - NEW: Headers: Content-Type: application/json
   - NEW: Body: { texts: [text] }

3. Response Parsing (splade-service.ts:76-87):
   - OLD: Expects { output: { data: [{ embedding: number[] }] } }
   - OLD: Converts dense array to sparse format
   - NEW: Expects { vectors: [{ indices: number[], values: number[] }] }
   - NEW: Uses sparse format directly

4. Model:
   - OLD: naver/splade-v3 (SPLADE model)
   - NEW: BM42 (sparse retrieval model)

Files updated for BM42 API:
- ✅ /lib/advanced-search/splade-service.ts (completed)
- ✅ /lib/advanced-search/⏺ SparseFlowTrace.md (updated documentation)
