#!/usr/bin/env python3
"""
Symptom Deduplication and Classification Enhancement Project

This script implements tasks 1 and 2 from the PRD:
- Task 1: Data Loading and Preparation
- Task 2: Vector Database Setup and Population

Author: AI Assistant
Date: 2024
"""

import json
import hashlib
import logging
import os
import sys
import argparse
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import numpy as np
from tqdm import tqdm
import time

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()  # Load .env file if it exists
except ImportError:
    # python-dotenv not installed, continue without it
    pass

# Import required libraries with error handling
try:
    from qdrant_client import QdrantClient
    from qdrant_client.models import Distance, VectorParams, PointStruct
except ImportError:
    print("Error: qdrant-client not installed. Please run: pip install qdrant-client")
    sys.exit(1)

try:
    import voyageai
except ImportError:
    print("Error: voyageai not installed. Please run: pip install voyageai")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('dedupSymptoms.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class SymptomProcessor:
    """Main class for processing symptom data and populating vector database."""
    
    def __init__(self, project_root: str = None):
        """Initialize the processor with project paths."""
        if project_root is None:
            self.project_root = Path(__file__).parent
        else:
            self.project_root = Path(project_root)
        
        # Set up paths
        self.data_file = self.project_root.parent / "data" / "gsheet_id.json"
        self.qdrant_path = self.project_root / "qdrant_db"
        
        # Initialize components
        self.qdrant_client = None
        self.voyage_client = None
        
        # Data storage
        self.original_data = []
        self.processed_data = []
        self.id_mapping = {}
        
        # Test mode settings
        self.test_mode = False
        self.test_limit = 50
        
        # Statistics
        self.stats = {
            'total_entries': 0,
            'valid_entries': 0,
            'invalid_entries': 0,
            'empty_combined_text': 0,
            'partial_data_entries': 0
        }
        
    def generate_unique_id(self, data_entry: Dict[str, Any], index: int) -> int:
        """
        Generate deterministic unique ID for each JSON entry.
        
        Args:
            data_entry: The JSON data entry
            index: Original position in the array
            
        Returns:
            int: Unique ID as integer (compatible with Qdrant)
        """
        # Create content string from key fields for consistent hashing (updated for combined text)
        content_parts = [
            str(data_entry.get('Product Area/Team', '')),
            str(data_entry.get('Ticket Type (Symptom) Definition', '')),
            str(data_entry.get('Reason', '')),
            str(index)
        ]
        content = '|'.join(content_parts)
        
        # Generate MD5 hash and convert to integer for Qdrant compatibility
        hash_hex = hashlib.md5(content.encode('utf-8')).hexdigest()[:16]
        unique_id = int(hash_hex, 16) % (2**63 - 1)  # Ensure it fits in 64-bit signed int
        return unique_id
    
    def create_combined_text(self, data_entry: Dict[str, Any]) -> str:
        """
        Create combined text from Product Area/Team, Ticket Type (Symptom) Definition, and Reason.
        
        Args:
            data_entry: The JSON data entry
            
        Returns:
            str: Combined text with 2 whitespaces between fields
        """
        # Extract the three fields
        product_area = str(data_entry.get('Product Area/Team', '')).strip()
        symptom_def = str(data_entry.get('Ticket Type (Symptom) Definition', '')).strip()
        reason = str(data_entry.get('Reason', '')).strip()
        
        # Combine with 2 whitespaces (as specified in PRD)
        combined_text = f"{product_area}  {symptom_def}  {reason}"
        
        return combined_text.strip()  # Remove leading/trailing whitespace
    
    def load_json_data(self) -> bool:
        """
        Load and parse the JSON data file.
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Loading JSON data from: {self.data_file}")
            
            if not self.data_file.exists():
                logger.error(f"Data file not found: {self.data_file}")
                return False
            
            # Get file size for progress tracking
            file_size = self.data_file.stat().st_size
            logger.info(f"File size: {file_size / (1024*1024):.2f} MB")
            
            # Load JSON data
            with open(self.data_file, 'r', encoding='utf-8') as file:
                self.original_data = json.load(file)
            
            self.stats['total_entries'] = len(self.original_data)
            logger.info(f"Successfully loaded {self.stats['total_entries']} entries")
            
            return True
            
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON format: {e}")
            return False
        except Exception as e:
            logger.error(f"Error loading JSON data: {e}")
            return False
    
    def validate_and_clean_data(self) -> List[Dict[str, Any]]:
        """
        Validate and clean the loaded data.
        
        Returns:
            List[Dict]: List of valid, cleaned data entries with generated IDs
        """
        logger.info("Starting data validation and cleaning...")
        
        # Apply test mode limit if enabled
        data_to_process = self.original_data
        if self.test_mode:
            data_to_process = self.original_data[:self.test_limit]
            logger.info(f"🧪 TEST MODE: Processing only first {len(data_to_process)} entries")
        
        valid_entries = []
        
        for index, entry in enumerate(tqdm(data_to_process, desc="Validating data")):
            # Generate unique ID for this entry
            unique_id = self.generate_unique_id(entry, index)
            
            # Store ID mapping
            self.id_mapping[unique_id] = {
                'original_index': index,
                'original_data': entry
            }
            
            # Create combined text from the three required fields
            combined_text = self.create_combined_text(entry)
            
            # Validate that we have meaningful content
            if not combined_text or combined_text.strip() == '':
                self.stats['empty_combined_text'] += 1
                self.stats['invalid_entries'] += 1
                logger.debug(f"Entry {index} has empty combined text")
                continue
            
            # Check if we have partial data (some fields missing)
            product_area = str(entry.get('Product Area/Team', '')).strip()
            symptom_def = str(entry.get('Ticket Type (Symptom) Definition', '')).strip()
            reason = str(entry.get('Reason', '')).strip()
            
            missing_fields = []
            if not product_area: missing_fields.append('Product Area/Team')
            if not symptom_def: missing_fields.append('Ticket Type (Symptom) Definition')
            if not reason: missing_fields.append('Reason')
            
            if missing_fields:
                self.stats['partial_data_entries'] += 1
                logger.debug(f"Entry {index} missing fields: {', '.join(missing_fields)}")
            
            # Create enriched entry with generated ID and combined text
            enriched_entry = {
                'id': unique_id,
                'combined_text': combined_text,
                'original_index': index,
                'original_data': entry,
                'missing_fields': missing_fields  # Track what's missing for analytics
            }
            
            valid_entries.append(enriched_entry)
            self.stats['valid_entries'] += 1
        
        # Log statistics
        logger.info(f"Data validation complete:")
        logger.info(f"  Total entries: {self.stats['total_entries']}")
        logger.info(f"  Valid entries: {self.stats['valid_entries']}")
        logger.info(f"  Invalid entries: {self.stats['invalid_entries']}")
        logger.info(f"  Empty combined text: {self.stats['empty_combined_text']}")
        logger.info(f"  Entries with partial data: {self.stats['partial_data_entries']}")
        
        self.processed_data = valid_entries
        return valid_entries
    
    def setup_qdrant(self) -> bool:
        """
        Initialize Qdrant database and create collection.
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info("Setting up Qdrant database...")
            
            # Create Qdrant client (local, disk-persisted)
            self.qdrant_client = QdrantClient(path=str(self.qdrant_path))
            
            # Collection configuration
            collection_name = "symptom_definitions"
            vector_size = 1024  # For Voyage AI voyage-3.5 model
            
            # Check if collection exists
            if self.qdrant_client.collection_exists(collection_name):
                logger.info(f"Collection '{collection_name}' already exists")
                return True
            
            # Create collection with cosine similarity
            self.qdrant_client.create_collection(
                collection_name=collection_name,
                vectors_config=VectorParams(
                    size=vector_size,
                    distance=Distance.COSINE
                )
            )
            
            logger.info(f"Created collection '{collection_name}' with vector size {vector_size}")
            return True
            
        except Exception as e:
            logger.error(f"Error setting up Qdrant: {e}")
            return False
    
    def setup_embedding_model(self) -> bool:
        """
        Initialize the Voyage AI embedding client.
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info("Setting up Voyage AI embedding client...")
            
            # Check for API key
            api_key = os.getenv('VOYAGE_API_KEY')
            if not api_key:
                logger.error("VOYAGE_API_KEY environment variable not set. Please set it with your Voyage AI API key.")
                return False
            
            # Initialize Voyage AI client
            self.voyage_client = voyageai.Client(api_key=api_key)
            
            # Test the connection with a simple embedding request
            test_result = self.voyage_client.embed(
                texts=["test connection"],
                model="voyage-3.5",
                input_type="document"
            )
            
            logger.info(f"Successfully initialized Voyage AI client with model: voyage-3.5")
            logger.info(f"Test embedding dimensions: {len(test_result.embeddings[0])}")
            return True
            
        except Exception as e:
            logger.error(f"Error setting up Voyage AI client: {e}")
            return False
    
    def vectorize_text_batch(self, texts: List[str], batch_size: int = 100) -> List[List[float]]:
        """
        Vectorize text in batches using Voyage AI.
        
        Args:
            texts: List of text strings to vectorize
            batch_size: Number of texts to process at once (max 1000 for Voyage AI)
            
        Returns:
            List[List[float]]: List of embedding vectors
        """
        logger.info(f"Vectorizing {len(texts)} texts using Voyage AI in batches of {batch_size}")
        
        all_embeddings = []
        
        # Process in batches with progress bar
        for i in tqdm(range(0, len(texts), batch_size), desc="Generating embeddings"):
            batch_texts = texts[i:i + batch_size]
            
            try:
                # Call Voyage AI API
                result = self.voyage_client.embed(
                    texts=batch_texts,
                    model="voyage-3.5",
                    input_type="document",  # Optimized for document storage
                    truncation=True  # Handle long texts gracefully
                )
                
                # Add embeddings to our collection
                all_embeddings.extend(result.embeddings)
                
                # Small delay to be respectful to the API
                time.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Error generating embeddings for batch {i//batch_size + 1}: {e}")
                raise
        
        logger.info(f"Successfully generated {len(all_embeddings)} embeddings")
        return all_embeddings
    
    def store_vectors_in_qdrant(self, batch_size: int = 100) -> bool:
        """
        Store vectors in Qdrant database using batch upsert.
        
        Args:
            batch_size: Number of points to upsert at once
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            collection_name = "symptom_definitions"
            
            # Extract combined texts for vectorization
            texts = [entry['combined_text'] for entry in self.processed_data]
            
            # Vectorize all texts
            logger.info("Generating embeddings for all texts...")
            embeddings = self.vectorize_text_batch(texts)
            
            # Prepare points for batch upsert
            logger.info("Preparing points for Qdrant storage...")
            points = []
            
            for i, (entry, embedding) in enumerate(zip(self.processed_data, embeddings)):
                point = PointStruct(
                    id=entry['id'],  # Use our generated unique ID
                    vector=embedding,  # embedding is already a list from Voyage AI
                    payload={
                        'combined_text': entry['combined_text'],
                        'original_index': entry['original_index'],
                        'original_data': entry['original_data'],
                        'missing_fields': entry['missing_fields']
                    }
                )
                points.append(point)
            
            # Batch upsert to Qdrant
            logger.info(f"Storing {len(points)} points in Qdrant (batch size: {batch_size})...")
            
            for i in tqdm(range(0, len(points), batch_size), desc="Uploading to Qdrant"):
                batch = points[i:i + batch_size]
                
                operation_info = self.qdrant_client.upsert(
                    collection_name=collection_name,
                    points=batch,
                    wait=True
                )
                
                # Small delay to avoid overwhelming the database
                time.sleep(0.1)
            
            logger.info(f"Successfully stored all vectors in Qdrant")
            return True
            
        except Exception as e:
            logger.error(f"Error storing vectors in Qdrant: {e}")
            return False
    
    def run_tasks_1_and_2(self) -> bool:
        """
        Execute tasks 1 and 2 from the PRD.
        
        Returns:
            bool: True if all tasks completed successfully
        """
        logger.info("=== Starting Tasks 1 and 2 Execution ===")
        
        # Task 1.1: Load JSON Data
        logger.info("Task 1.1: Loading JSON Data")
        if not self.load_json_data():
            logger.error("Failed to load JSON data")
            return False
        
        # Task 1.2 & 1.3: Generate Unique IDs and Validate Data
        logger.info("Task 1.2 & 1.3: Generating IDs and Validating Data")
        valid_data = self.validate_and_clean_data()
        if not valid_data:
            logger.error("No valid data found after validation")
            return False
        
        # Task 2.1: Qdrant Database Setup
        logger.info("Task 2.1: Setting up Qdrant Database")
        if not self.setup_qdrant():
            logger.error("Failed to setup Qdrant database")
            return False
        
        # Task 2.2: Text Vectorization Setup  
        logger.info("Task 2.2: Setting up Voyage AI Embeddings")
        if not self.setup_embedding_model():
            logger.error("Failed to setup Voyage AI client")
            return False
        
        # Task 2.3: Vector Storage
        logger.info("Task 2.3: Storing Vectors in Qdrant")
        if not self.store_vectors_in_qdrant():
            logger.error("Failed to store vectors in Qdrant")
            return False
        
        logger.info("=== Tasks 1 and 2 Completed Successfully ===")
        return True
    
    def close_connections(self):
        """Close all open connections to free resources."""
        if self.qdrant_client:
            try:
                self.qdrant_client.close()
                logger.info("Closed Qdrant client connection")
            except Exception as e:
                logger.warning(f"Error closing Qdrant connection: {e}")
            self.qdrant_client = None


def main():
    """Main execution function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Symptom deduplication - Tasks 1 & 2 (Data Loading and Vector Database)')
    parser.add_argument('--test', action='store_true',
                       help='Test mode: limit processing for quick testing (limits to first 50 entries)')
    args = parser.parse_args()
    
    if args.test:
        print("🧪 TEST MODE: Limiting processing for quick testing")
    
    logger.info("Starting Symptom Deduplication Process")
    
    try:
        # Initialize processor
        processor = SymptomProcessor()
        
        # Apply test mode limitations if needed
        if args.test:
            # We'll modify the processor to limit data processing in test mode
            processor.test_mode = True
            processor.test_limit = 50  # Limit to first 50 entries for testing
        else:
            processor.test_mode = False
        
        # Run tasks 1 and 2
        success = processor.run_tasks_1_and_2()
        
        if success:
            logger.info("All tasks completed successfully!")
            
            # Print final statistics
            print("\n=== Final Statistics ===")
            print(f"Total entries processed: {processor.stats['total_entries']}")
            print(f"Valid entries: {processor.stats['valid_entries']}")
            print(f"Invalid entries: {processor.stats['invalid_entries']}")
            print(f"Empty combined text: {processor.stats['empty_combined_text']}")
            print(f"Entries with partial data: {processor.stats['partial_data_entries']}")
            print(f"Vectors stored in Qdrant: {processor.stats['valid_entries']}")
            
            if args.test:
                print("🧪 TEST MODE: Processing limited to first 50 entries")
            
        else:
            logger.error("Tasks failed to complete")
            return 1
        
    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
