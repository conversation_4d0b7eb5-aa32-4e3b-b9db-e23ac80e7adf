# Advanced Search API Implementation Guide

## Problem Statement & Goals

### Current State

- Search functionality is entirely frontend-based using simple string matching
- No backend search capabilities or ranking algorithms
- Limited to basic text filtering in JavaScript
- No support for semantic search or advanced retrieval methods

### Goals

We want to implement a sophisticated backend search API that supports multiple search modalities:

1. **SPLADE Sparse Vectors** - Neural sparse retrieval for semantic understanding
2. **Exact Word Matching** - Traditional keyword-based search
3. **Exact Phrase Matching** - Precise phrase-level matching
4. **Vector Similarity** - Dense vector semantic search
5. **Hybrid Search** - Weighted combination of vector similarity + exact phrase matching

### Success Criteria

- ✅ Backend API that can handle multiple search types
- ✅ Configurable search weights and parameters
- ✅ Support for snippet extraction with ±10 words context
- ✅ Performance suitable for real-time search (sub-500ms response times)
- ✅ Maintains compatibility with existing frontend interface
- ✅ Scalable architecture that can handle growing knowledge base

## Product Requirements Document (PRD)

### 1. Technical Architecture Decision

**FalkorDB vs Qdrant Analysis:**

| Feature               | FalkorDB      | Qdrant       | Recommendation   |
| --------------------- | ------------- | ------------ | ---------------- |
| Graph Relationships   | ✅ Native     | ❌ Limited   | Keep in FalkorDB |
| Vector Similarity     | ❌ No support | ✅ Optimized | Use Qdrant       |
| SPLADE Sparse Vectors | ❌ No support | ✅ Native    | Use Qdrant       |
| Exact Text Search     | ⚠️ Basic      | ✅ Advanced  | Use Qdrant       |
| Hybrid Search         | ❌ No support | ✅ Built-in  | Use Qdrant       |

**Recommended Architecture: Dual Database Approach**

- **FalkorDB**: Maintain hierarchical node structure and relationships
- **Qdrant Cloud**: Duplicate searchable content for advanced search capabilities
  - Native cosine similarity support
  - Built-in filtering and hybrid search functionality
  - Optimized for high-dimensional vectors (Voyage AI's 1024-dimension embeddings)
  - Cloud-hosted for scalability and reliability
- **Sync Strategy**: Keep both databases synchronized via API layer
- **Collection Naming**: Qdrant collection names match FalkorDB graph names
  - **Primary HR Collection**: `mock_hr` (contains all HR policies and procedures)
  - Other collections: `knowledge_graph`, etc.

### 2. API Requirements

#### 2.1 Search Endpoint: `POST /api/search/advanced`

**Request Schema:**

```typescript
interface AdvancedSearchRequest {
  query: string;
  graph_name: string; // FalkorDB graph name (also used as Qdrant collection name)
  search_methods: {
    splade?: { enabled: boolean; weight: number };
    exact_words?: { enabled: boolean; weight: number };
    exact_phrase?: { enabled: boolean; weight: number };
    vector_similarity?: {
      enabled: boolean;
      weight: number;
      model?: "voyage-3.5";
      dimension?: 256 | 512 | 1024 | 2048;
    };
    hybrid?: {
      enabled: boolean;
      vector_weight: number;
      phrase_weight: number;
    };
  };
  filters?: {
    section_path?: string[]; // Hierarchical path filter (e.g., ["Employers and Admins", "People Management"])
    article_type?: "section" | "article"; // Filter by content type
    level?: number; // Hierarchy level filter
    update_date_after?: string; // ISO date string
  };
  result_options?: {
    limit?: number;
    offset?: number;
    include_snippets?: boolean;
    snippet_context_words?: number;
  };
}
```

**Response Schema:**

```typescript
interface AdvancedSearchResponse {
  success: boolean;
  query: string;
  graph_name: string;
  collection_name: string; // Same as graph_name
  total_results: number;
  execution_time_ms: number;
  results: SearchResult[];
  method_scores?: {
    [method: string]: {
      results_count: number;
      avg_score: number;
    };
  };
}

interface SearchResult {
  record: HRRecord; // Updated Record type for HR data structure
  relevance_score: number;
  method_contributions: {
    [method: string]: number;
  };
  snippets?: SearchSnippet[];
}

interface HRRecord {
  id: string;
  title: string;
  content?: string;
  summary?: string;
  fullContent?: string;
  updateDate?: string;
  breadcrumb: string; // Generated from hierarchy (e.g., "Employers and Admins > People Management > Hiring")
  section_path: string[]; // Array of parent section titles
  article_type: "section" | "article";
  level: number; // Hierarchy depth
  nodeType: "category" | "record";
}

interface SearchSnippet {
  text: string;
  context_before: string;
  context_after: string;
  start_position: number;
  end_position: number;
  relevance_score: number;
}
```

### 3. Search Method Specifications

#### 3.1 SPLADE Sparse Vectors

- **Model**: `naver/splade-v3` (via RunPod API)
- **Implementation**: Generate sparse vectors using RunPod serverless endpoint
- **RunPod Integration**: Use existing `testRunPod.js` configuration for SPLADE inference
- **API Endpoint**: `https://api.runpod.ai/v2/i57fast0l4w9ig/runsync`
- **Storage**: Store sparse vectors in Qdrant Cloud collections with named vector format
- **Scoring**: Use dot product similarity for sparse vector matching

#### 3.2 Exact Word Matching

- **Implementation**: Tokenize query and count word matches
- **Scoring**: TF-IDF or BM25-style scoring
- **Case Sensitivity**: Configurable (default: case-insensitive)

#### 3.3 Exact Phrase Matching

- **Implementation**: Substring search with position tracking
- **Scoring**: Based on phrase frequency and document length
- **Proximity**: Support for near-phrase matching (within N words)

#### 3.4 Vector Similarity

- **Model**: Voyage AI's `voyage-3.5` embedding model
- **Context Length**: 32,000 tokens
- **Embedding Dimension**: 1024 (default), with support for 256, 512, and 2048 dimensions
- **Optimized for**: General-purpose and multilingual retrieval quality
- **Input Types**: Support for `query` and `document` input types with automatic prompt injection
- **Token Limits**: 320K total tokens per batch for `voyage-3.5`
- **Similarity**: Cosine similarity (supported natively by Qdrant)
- **Implementation**: Use Qdrant Cloud's native cosine similarity search functionality

#### 3.5 Hybrid Search

- **Implementation**: Use Qdrant's Query API with built-in hybrid search capabilities
- **Fusion Methods**:
  - **RRF (Reciprocal Rank Fusion)**: Considers positions of results within each query, boosts documents appearing closer to top in multiple queries
  - **DBSF (Distribution-Based Score Fusion)**: Normalizes scores using mean ± 3rd standard deviation as limits, then sums scores across queries
- **Prefetch Architecture**: Use `prefetch` parameter to enable sub-requests that combine multiple search methods
- **Default Weights**: 0.7 vector + 0.3 phrase (when using manual weighted combination)
- **Qdrant Integration**: Native Query API eliminates need for external result fusion

## Qdrant Hybrid Search Advantages

### 1. **Native Query API (v1.10+)**

- **Unified Interface**: Single endpoint for all search types (dense, sparse, hybrid, multi-stage)
- **Server-Side Processing**: All fusion and re-ranking happens on Qdrant server
- **No External Dependencies**: Eliminates need for additional services like Elasticsearch

### 2. **Built-in Fusion Methods**

- **RRF (Reciprocal Rank Fusion)**: Position-based ranking that favors documents appearing high in multiple result sets
- **DBSF (Distribution-Based Score Fusion)**: Statistical normalization using mean ± 3σ for score fusion
- **Automatic Score Normalization**: No manual score normalization required

### 3. **Prefetch Architecture**

- **Nested Queries**: Support for multi-level prefetch operations
- **Flexible Staging**: Can chain different vector types (sparse → dense → full-precision)
- **Efficient Candidate Selection**: Reduces computational overhead by filtering candidates early

### 4. **Multi-Vector Support**

- **Named Vectors**: Multiple vector representations per point (dense, sparse, different dimensions)
- **Late Interaction Models**: Support for ColBERT-style multi-vector representations
- **Matryoshka Embeddings**: Progressive refinement with different dimensionalities

### 5. **Performance Benefits**

- **Single Network Round-Trip**: All operations happen server-side
- **Optimized Indexing**: Native sparse vector indices with efficient storage
- **Reduced Memory Usage**: No need to load multiple result sets in application memory

## Implementation Examples

### Environment Configuration

```bash
# .env file configuration
# Qdrant Cloud Configuration
QDRANT_URL=https://your-cluster-url.qdrant.tech:6333
QDRANT_API_KEY=your-qdrant-cloud-api-key

# Voyage AI Configuration
VOYAGE_API_KEY=your-voyage-api-key

# RunPod SPLADE Configuration
RUNPOD_API_KEY=your-runpod-api-key
RUNPOD_ENDPOINT_ID=i57fast0l4w9ig

# FalkorDB Configuration (existing)
FALKOR_HOST=your-falkor-host
FALKOR_USERNAME=your-falkor-username
FALKOR_PASSWORD=your-falkor-password
FALKOR_PORT=6379
```

### Voyage AI Integration

```python
import voyageai
import os

# Initialize Voyage AI client using environment variable
vo = voyageai.Client(api_key=os.getenv("VOYAGE_API_KEY"))

# Generate embeddings using voyage-3.5
def generate_embeddings(texts):
    result = vo.embed(
        texts=texts,
        model="voyage-3.5",
        input_type="document",
        output_dimension=1024  # Default dimension (supports 256, 512, 1024, 2048)
    )
    return result.embeddings

# Response structure: EmbeddingsObject with attributes:
# - embeddings: List[List[float]] - list of embedding vectors
# - total_tokens: int - total number of tokens processed

# Example with proper error handling
def safe_generate_embeddings(texts, model="voyage-3.5", input_type="document"):
    try:
        result = vo.embed(
            texts=texts,
            model=model,
            input_type=input_type,
            output_dimension=1024,
            truncation=True  # Automatically truncate if text exceeds context length
        )
        return result.embeddings, result.total_tokens
    except Exception as e:
        print(f"Error generating embeddings: {e}")
        return None, 0
```

### RunPod SPLADE Integration

```typescript
import fetch from "node-fetch";

// RunPod SPLADE sparse vector generation
async function generateSpladeVectors(texts: string[]): Promise<any[]> {
  const runpodUrl = `https://api.runpod.ai/v2/${process.env.RUNPOD_ENDPOINT_ID}/runsync`;
  const apiKey = process.env.RUNPOD_API_KEY;

  const results = [];

  for (const text of texts) {
    try {
      const response = await fetch(runpodUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${apiKey}`,
        },
        body: JSON.stringify({
          input: {
            model: "naver/splade-v3",
            input: text,
          },
        }),
      });

      if (!response.ok) {
        throw new Error(`RunPod API error: ${response.status}`);
      }

      const data = await response.json();

      // Extract sparse vector from RunPod response
      const sparseVector = {
        indices: data.output?.indices || [],
        values: data.output?.values || [],
      };

      results.push(sparseVector);
    } catch (error) {
      console.error(
        `Error generating SPLADE vector for text: ${text.slice(0, 50)}...`,
        error
      );
      // Return empty sparse vector on error
      results.push({ indices: [], values: [] });
    }
  }

  return results;
}

// Batch processing for efficient SPLADE generation
async function batchGenerateSpladeVectors(
  texts: string[],
  batchSize: number = 10
): Promise<any[]> {
  const results = [];

  for (let i = 0; i < texts.length; i += batchSize) {
    const batch = texts.slice(i, i + batchSize);
    console.log(
      `Processing SPLADE batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(
        texts.length / batchSize
      )}`
    );

    const batchResults = await generateSpladeVectors(batch);
    results.push(...batchResults);

    // Add delay between batches to avoid rate limiting
    if (i + batchSize < texts.length) {
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }
  }

  return results;
}

// Safe SPLADE generation with error handling
async function safeGenerateSpladeVectors(texts: string[]): Promise<any[]> {
  try {
    return await batchGenerateSpladeVectors(texts);
  } catch (error) {
    console.error("Error in SPLADE generation:", error);
    // Return empty sparse vectors for all texts on error
    return texts.map(() => ({ indices: [], values: [] }));
  }
}
```

### Qdrant Cloud Setup

```python
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams
import os

# Initialize Qdrant Cloud client using environment variables
client = QdrantClient(
    url=os.getenv("QDRANT_URL"),
    api_key=os.getenv("QDRANT_API_KEY")
)

# Create collection with dynamic naming based on FalkorDB graph
def create_search_collection(graph_name: str):
    collection_name = graph_name  # e.g., "mock_hr", "knowledge_graph"

    # Check if collection already exists
    if client.collection_exists(collection_name):
        print(f"Collection '{collection_name}' already exists")
        return True

    # Create collection with both dense and sparse vectors
    client.create_collection(
        collection_name=collection_name,
        vectors_config={
            "dense": VectorParams(
                size=1024,  # Voyage-3.5 default dimension
                distance=Distance.COSINE
            ),
            "sparse": VectorParams(
                size=30522,  # SPLADE vocabulary size
                distance=Distance.DOT,
                sparse=True
            )
        }
    )
    print(f"Created collection '{collection_name}' with dense + sparse vectors for graph '{graph_name}'")
    return True
```

### HR Data Structure Processing

```python
def flatten_hr_hierarchy(hr_data, parent_path=[], level=0):
    """
    Flatten hierarchical HR data structure for search indexing.

    Args:
        hr_data: List of HR sections from mockHRdata.json
        parent_path: Current path in hierarchy
        level: Current depth level

    Returns:
        List of flattened records for search indexing
    """
    flattened_records = []

    for section in hr_data:
        current_path = parent_path + [section["title"]]
        breadcrumb = " > ".join(current_path)

        # Add section as searchable record
        section_record = {
            "id": f"section-{'-'.join(current_path).lower().replace(' ', '-')}",
            "title": section["title"],
            "content": section["content"],
            "breadcrumb": breadcrumb,
            "section_path": current_path,
            "article_type": "section",
            "level": level,
            "nodeType": "category"
        }
        flattened_records.append(section_record)

        # Process articles in this section
        for article in section.get("articles", []):
            article_record = {
                "id": f"article-{article['title'].lower().replace(' ', '-')}",
                "title": article["title"],
                "summary": article["summary"],
                "fullContent": article["fullContent"],
                "updateDate": article["updateDate"],
                "breadcrumb": f"{breadcrumb} > {article['title']}",
                "section_path": current_path,
                "article_type": "article",
                "level": level + 1,
                "nodeType": "record"
            }
            flattened_records.append(article_record)

        # Recursively process nested sections
        if "sections" in section:
            nested_records = flatten_hr_hierarchy(
                section["sections"],
                current_path,
                level + 1
            )
            flattened_records.extend(nested_records)

    return flattened_records

# HR Data Sync Function
async def sync_falkor_to_qdrant(graph_name: str, api_base_url: str = "http://localhost:3000", batch_size: int = 1000):
    """
    Sync HR data from FalkorDB to Qdrant via API calls with pagination support.
    This is the recommended approach for production.
    """
    collection_name = graph_name

    # 1. Fetch data from FalkorDB via API with pagination
    all_nodes = []
    all_records = []
    page = 0

    try:
        import httpx  # async HTTP client

        async with httpx.AsyncClient(timeout=60.0) as client:
            while True:
                # Fetch data with pagination
                response = await client.get(
                    f"{api_base_url}/api/data-falkor?graph={graph_name}&page={page}&limit={batch_size}"
                )
                response.raise_for_status()
                falkor_data = response.json()

                if not falkor_data.get("success"):
                    raise Exception(f"FalkorDB API error: {falkor_data.get('error')}")

                page_nodes = falkor_data["data"]["nodes"]
                page_records = falkor_data["data"]["records"]

                # Add to collections
                all_nodes.extend(page_nodes)
                all_records.extend(page_records)

                print(f"Fetched page {page}: {len(page_nodes)} nodes, {len(page_records)} records")

                # Check if we have more data
                has_more = falkor_data.get("meta", {}).get("has_more", False)
                if not has_more or (len(page_nodes) == 0 and len(page_records) == 0):
                    break

                page += 1

            print(f"Total fetched: {len(all_nodes)} nodes and {len(all_records)} records from FalkorDB")

    except Exception as e:
        print(f"Error fetching from FalkorDB API: {e}")
        return False

        # 2. Transform FalkorDB data to search-optimized format
    flat_records = []

    # Process category nodes
    for node in all_nodes:
        if node.get("nodeType") == "category":
            record = {
                "id": f"node-{node['id']}",
                "title": node["name"],
                "content": node.get("summary", ""),
                "summary": node.get("refinedSummary", ""),
                "breadcrumb": node.get("path", ""),
                "section_path": node.get("path", "").split(" > "),
                "article_type": "section",
                "level": node.get("level", 0),
                "nodeType": "category",
                "country": node.get("country", "")
            }
            flat_records.append(record)

    # Process record nodes
    for record in all_records:
        search_record = {
            "id": f"record-{record['id']}",
            "title": record["title"],
            "content": record.get("cleanedBody", ""),
            "fullContent": record.get("cleanedBody", ""),
            "breadcrumb": record.get("breadcrumb", ""),
            "section_path": record.get("breadcrumb", "").split(" > "),
            "article_type": "article",
            "level": len(record.get("breadcrumb", "").split(" > ")) - 1,
            "nodeType": "record",
            "country": record.get("country", ""),
            "htmlUrl": record.get("htmlUrl", ""),
            "sectionId": record.get("sectionId"),
            "recordId": record.get("recordId")
        }
        flat_records.append(search_record)

    # 3. Generate combined text for embedding
    texts_for_embedding = []
    for record in flat_records:
        combined_text = f"{record['title']} {record.get('content', '')} {record.get('summary', '')} {record.get('fullContent', '')}"
        texts_for_embedding.append(combined_text.strip())

    # 4. Generate embeddings
    embeddings = generate_embeddings(texts_for_embedding)

    # 5. Prepare points for Qdrant
    points = []
    for i, (record, embedding) in enumerate(zip(flat_records, embeddings)):
        point = PointStruct(
            id=record["id"],
            vector=embedding,
            payload=record
        )
        points.append(point)

    # 6. Store in Qdrant Cloud
    try:
        client.upsert(
            collection_name=collection_name,
            points=points,
            wait=True
        )

        print(f"Successfully synced {len(points)} records to Qdrant collection '{collection_name}'")
        return True

    except Exception as e:
        print(f"Error storing in Qdrant: {e}")
        return False

# Alternative: Direct data sync (for initial setup or batch operations)
def sync_hr_data_to_qdrant_direct(hr_data, graph_name):
    """
    Direct sync from source files to Qdrant.
    Use this for initial setup or when bypassing FalkorDB.
    """
    collection_name = graph_name

    # Flatten hierarchical structure
    flat_records = flatten_hr_hierarchy(hr_data)

    # Generate combined text for embedding
    texts_for_embedding = []
    for record in flat_records:
        combined_text = f"{record['title']} {record.get('content', '')} {record.get('summary', '')} {record.get('fullContent', '')}"
        texts_for_embedding.append(combined_text.strip())

    # Generate embeddings
    embeddings = generate_embeddings(texts_for_embedding)

    # Prepare points for Qdrant
    points = []
    for i, (record, embedding) in enumerate(zip(flat_records, embeddings)):
        point = PointStruct(
            id=record["id"],
            vector=embedding,
            payload=record
        )
        points.append(point)

    # Store in Qdrant
    client.upsert(
        collection_name=collection_name,
        points=points,
        wait=True
    )

    print(f"Synced {len(points)} records to Qdrant collection '{collection_name}'")
    return True
```

### Qdrant Hybrid Search Implementation

```python
from qdrant_client import models

# Basic Hybrid Search using RRF with dynamic collection
def hybrid_search_rrf(query_text, graph_name):
    collection_name = graph_name  # Use graph name as collection name (e.g., 'mock_hr')

    # Generate embeddings
    dense_vector = generate_embeddings([query_text])[0]
    sparse_vector = generate_sparse_vector(query_text)

    results = client.query_points(
        collection_name=collection_name,
        prefetch=[
            models.Prefetch(
                query=models.SparseVector(
                    indices=sparse_vector["indices"],
                    values=sparse_vector["values"]
                ),
                using="sparse",
                limit=20,
            ),
            models.Prefetch(
                query=dense_vector,
                using="dense",
                limit=20,
            ),
        ],
        query=models.FusionQuery(fusion=models.Fusion.RRF),
        limit=10,
    )
    return results

# HR-specific search with section filtering
def search_hr_content(query_text, graph_name, section_path=None, article_type=None):
    collection_name = graph_name  # For HR queries, typically 'mock_hr'

    # Build filter conditions
    filter_conditions = []
    if section_path:
        filter_conditions.append(
            models.FieldCondition(
                key="section_path",
                match=models.MatchAny(any=section_path)
            )
        )
    if article_type:
        filter_conditions.append(
            models.FieldCondition(
                key="article_type",
                match=models.MatchValue(value=article_type)
            )
        )

    # Combine filters
    search_filter = None
    if filter_conditions:
        search_filter = models.Filter(must=filter_conditions)

    # Generate query embedding
    dense_vector = generate_embeddings([query_text])[0]

    # Search with filters
    results = client.search(
        collection_name=collection_name,
        query_vector=dense_vector,
        query_filter=search_filter,
        limit=10,
        with_payload=True
    )

    return results

# Multi-stage Search with Re-ranking
def multi_stage_hybrid_search(query_text, graph_name):
    collection_name = graph_name
    sparse_vector = generate_sparse_vector(query_text)
    dense_vector = generate_embeddings([query_text])[0]

    results = client.query_points(
        collection_name=collection_name,
        prefetch=models.Prefetch(
            prefetch=[
                models.Prefetch(
                    query=models.SparseVector(
                        indices=sparse_vector["indices"],
                        values=sparse_vector["values"]
                    ),
                    using="sparse",
                    limit=100,  # Larger candidate set
                ),
                models.Prefetch(
                    query=dense_vector,
                    using="dense",
                    limit=100,
                ),
            ],
            query=models.FusionQuery(fusion=models.Fusion.RRF),
            limit=50,  # Intermediate results
        ),
        # Second stage: Re-rank with full precision vectors
        query=dense_vector,
        using="dense_full_precision",
        limit=10,
    )
    return results

# DBSF (Distribution-Based Score Fusion) Example
def hybrid_search_dbsf(query_text, graph_name):
    collection_name = graph_name
    dense_vector = generate_embeddings([query_text])[0]
    sparse_vector = generate_sparse_vector(query_text)

    results = client.query_points(
        collection_name=collection_name,
        prefetch=[
            models.Prefetch(
                query=sparse_vector,
                using="sparse",
                limit=20,
            ),
            models.Prefetch(
                query=dense_vector,
                using="dense",
                limit=20,
            ),
        ],
        query=models.FusionQuery(fusion=models.Fusion.DBSF),
        limit=10,
    )
    return results
```

### FalkorDB API Pagination Implementation

The current FalkorDB API lacks pagination support. Here's how to add it:

```typescript
// Updated /api/data-falkor route with pagination
export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const graphName = url.searchParams.get("graph") || DEFAULT_GRAPH_NAME;
    const page = parseInt(url.searchParams.get("page") || "0");
    const limit = parseInt(url.searchParams.get("limit") || "1000");
    const offset = page * limit;

    // Paginated queries with SKIP and LIMIT
    const categoryQuery = `
      MATCH (c:Category)
      RETURN c
      ORDER BY c.level, c.path
      SKIP $offset LIMIT $limit
    `;

    const recordQuery = `
      MATCH (r:Record)
      OPTIONAL MATCH (c:Category)-[:CONTAINS]->(r)
      RETURN r, c
      ORDER BY r.title
      SKIP $offset LIMIT $limit
    `;

    // Count queries for pagination metadata
    const categoryCountQuery = `MATCH (c:Category) RETURN count(c) as total`;
    const recordCountQuery = `MATCH (r:Record) RETURN count(r) as total`;

    // Execute queries with pagination parameters
    const [categoryResult, recordResult, categoryCount, recordCount] =
      await Promise.all([
        executeQuery(graphName, categoryQuery, { offset, limit }),
        executeQuery(graphName, recordQuery, { offset, limit }),
        executeQuery(graphName, categoryCountQuery),
        executeQuery(graphName, recordCountQuery),
      ]);

    const totalCategories = categoryCount?.data?.[0]?.total || 0;
    const totalRecords = recordCount?.data?.[0]?.total || 0;

    const { nodes, records } = transformFalkorResultsToAPI(
      categoryResult?.data || [],
      recordResult?.data || []
    );

    // Calculate pagination metadata
    const hasMoreCategories = offset + limit < totalCategories;
    const hasMoreRecords = offset + limit < totalRecords;
    const hasMore = hasMoreCategories || hasMoreRecords;

    return NextResponse.json({
      success: true,
      data: { nodes, records },
      meta: {
        source: "falkordb",
        graph: graphName,
        timestamp: new Date().toISOString(),
        pagination: {
          page,
          limit,
          offset,
          has_more: hasMore,
          total_categories: totalCategories,
          total_records: totalRecords,
          returned_nodes: nodes.length,
          returned_records: records.length,
        },
      },
    });
  } catch (error) {
    // Error handling...
  }
}
```

### API Usage Examples

```bash
# Paginated fetch from FalkorDB (syncs to mock_hr collection in Qdrant)
curl "http://localhost:3000/api/data-falkor?graph=mock_hr&page=0&limit=500"
curl "http://localhost:3000/api/data-falkor?graph=mock_hr&page=1&limit=500"

# Search in HR knowledge base (uses mock_hr Qdrant collection)
curl -X POST "http://localhost:3000/api/search/advanced" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "best practices for hiring new employees",
    "graph_name": "mock_hr",
    "search_methods": {
      "vector_similarity": {
        "enabled": true,
        "weight": 0.7
      },
      "exact_phrase": {
        "enabled": true,
        "weight": 0.3
      }
    },
    "filters": {
      "section_path": ["Employers and Admins", "People Management"],
      "article_type": "article"
    },
    "result_options": {
      "limit": 10,
      "include_snippets": true,
      "snippet_context_words": 10
    }
  }'

# Search across all HR sections (uses mock_hr collection)
curl -X POST "http://localhost:3000/api/search/advanced" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "payroll tax requirements",
    "graph_name": "mock_hr",
    "search_methods": {
      "hybrid": {
        "enabled": true,
        "vector_weight": 0.6,
        "phrase_weight": 0.4
      }
    },
    "result_options": {
      "limit": 5
    }
  }'
```

## Implementation Plan

### Phase 1: Infrastructure Setup (Week 1)

#### Task 1.1: Qdrant Cloud Integration

- [ ] Set up Qdrant Cloud instance and obtain connection credentials
- [ ] Create Qdrant client configuration with cloud endpoint and API key
- [ ] Design collection schema for HR records with 1024-dimensional vectors
- [ ] Configure Qdrant collections using `"distance": "Cosine"` metric
- [ ] Set up proper indexing for efficient similarity search
- [ ] Implement dynamic collection naming based on FalkorDB graph names

#### Task 1.2: Data Migration Pipeline

- [ ] **Add pagination support to `/api/data-falkor` endpoint** (page, limit, has_more)
- [ ] Create sync service to duplicate FalkorDB HR data to Qdrant via API calls with pagination
- [ ] Implement `sync_falkor_to_qdrant()` function that reads from `/api/data-falkor` with batching
- [ ] Implement Voyage AI embedding generation pipeline using `voyage-3.5`
- [ ] Create SPLADE vector generation pipeline
- [ ] Set up data validation and integrity checks for HR data structure
- [ ] Configure Qdrant collections with cosine similarity metric
- [ ] Implement data transformation from FalkorDB format to search-optimized format
- [ ] Create fallback option for direct file sync during initial setup

#### Task 1.3: Base API Structure

- [ ] Create `/api/search/advanced` endpoint skeleton with graph_name parameter
- [ ] Implement request validation middleware for HR-specific filters
- [ ] Set up response formatting utilities for HR data structure
- [ ] Add error handling and logging

### Phase 2: Core Search Methods (Week 2)

#### Task 2.1: Exact Matching Implementation

- [ ] Implement exact word matching algorithm for HR content
- [ ] Implement exact phrase matching algorithm
- [ ] Add configurable scoring mechanisms
- [ ] Create unit tests for exact matching with HR data

#### Task 2.2: Vector Similarity Setup

- [ ] Integrate Voyage AI's `voyage-3.5` model for embeddings
- [ ] Implement vector similarity search via Qdrant Cloud with cosine similarity
- [ ] Set up Voyage AI API integration and authentication (use `VOYAGE_API_KEY` environment variable)
- [ ] Configure optimal embedding dimensions (1024 default, supports 256, 512, 2048)
- [ ] Implement proper input_type handling for queries vs documents
- [ ] Create vector search unit tests with Voyage embeddings and HR data

#### Task 2.3: SPLADE Integration - **COMPLETED** ✅

- [x] Set up SPLADE model inference via RunPod API
- [x] Implement sparse vector generation for HR content using RunPod
- [x] Integrate with Qdrant Cloud sparse vector search (named vectors)
- [x] Create SPLADE-specific tests with HR data
- [x] Update sync service to generate both dense and sparse vectors
- [x] Create re-sync script for adding sparse vectors to existing collections

### Phase 3: Advanced Features (Week 3)

#### Task 3.1: Hybrid Search Engine

- [ ] Implement Qdrant Query API with prefetch architecture
- [ ] Configure RRF (Reciprocal Rank Fusion) for basic hybrid search
- [ ] Set up DBSF (Distribution-Based Score Fusion) for advanced scoring
- [ ] Create multi-stage search pipelines with re-ranking
- [ ] Add configurable fusion method selection (RRF/DBSF)
- [ ] Test various hybrid search combinations and measure performance with HR data
- [ ] Implement HR-specific filtering (section_path, article_type, level)

#### Task 3.2: Snippet Extraction

- [ ] Implement context-aware snippet extraction for articles
- [ ] Add ±N words context functionality
- [ ] Create snippet relevance scoring
- [ ] Handle multiple snippets per document

#### Task 3.3: Performance Optimization

- [ ] Implement result caching layer for frequently searched HR topics
- [ ] Add query optimization logic
- [ ] Set up connection pooling for Qdrant Cloud
- [ ] Create performance monitoring

### Phase 4: Testing & Integration (Week 4)

#### Task 4.1: Comprehensive Testing

- [ ] Integration tests for all search methods with HR data
- [ ] **Test pagination functionality in FalkorDB API** (page boundaries, large datasets)
- [ ] **Test sync performance with paginated data retrieval**
- [ ] Performance benchmarking with HR-specific queries
- [ ] Load testing with realistic HR knowledge base queries
- [ ] Edge case testing for hierarchical HR structure
- [ ] **Memory usage testing during large dataset pagination**

#### Task 4.2: Frontend Integration

- [ ] Update frontend to use new search API with graph_name parameter
- [ ] Maintain backward compatibility
- [ ] Add search method selection UI
- [ ] Implement advanced search options for HR-specific filters

#### Task 4.3: Documentation & Monitoring

- [ ] API documentation with HR data examples
- [ ] **Document pagination parameters and usage patterns**
- [ ] Deployment guides for Qdrant Cloud setup
- [ ] Monitoring and alerting setup
- [ ] Performance dashboards
- [ ] **Add monitoring for sync performance and pagination metrics**

## Test Scripts Overview

### Test Structure

All test scripts should be placed in `@/instructTests/` folder with the following naming convention:

- `test-search-[method].ts` - Individual search method tests
- `test-search-integration.ts` - End-to-end integration tests with HR data
- `test-search-performance.ts` - Performance and load tests
- `run-search-tests.ts` - Test runner script

### Test Categories

1. **Unit Tests**

   - Individual search method accuracy with HR content
   - Score calculation validation
   - Snippet extraction correctness for HR articles

2. **Integration Tests**

   - API endpoint functionality with graph_name parameter
   - Database synchronization between FalkorDB and Qdrant
   - Multi-method search combinations with HR filters

3. **Performance Tests**

   - Response time benchmarks for HR queries
   - Concurrent request handling
   - Memory usage monitoring with large HR datasets

4. **Pagination Tests**

   - FalkorDB API pagination correctness
   - Sync function pagination handling
   - Large dataset pagination performance
   - Page boundary edge cases

5. **Data Quality Tests**
   - Search result relevance for HR-specific queries
   - Score distribution analysis
   - Edge case handling for hierarchical HR structure

### HR-Specific Test Data

```typescript
// Test queries specific to HR domain (uses mock_hr collection)
const hrTestQueries = [
  {
    query: "best practices for onboarding new employees",
    graph_name: "mock_hr",
    expected_sections: ["People Management", "Hiring and Onboarding"],
    expected_articles: ["Best Practices for Writing Job Descriptions"],
  },
  {
    query: "payroll tax requirements FUTA",
    graph_name: "mock_hr",
    expected_sections: ["Payroll", "Tax Information"],
    expected_articles: ["Understanding Your Company's FUTA and SUI Tax Rates"],
  },
  {
    query: "international contractor payments",
    graph_name: "mock_hr",
    expected_sections: [
      "Hiring and Onboarding",
      "Onboarding International Contractors",
    ],
    expected_articles: [
      "Key Considerations for Paying International Contractors",
    ],
  },
];
```

## Success Metrics

### Performance Targets

- **Response Time**: < 500ms for 95% of HR queries
- **Throughput**: > 100 concurrent requests/second
- **Accuracy**: > 85% relevance for HR test query set
- **Uptime**: 99.9% availability

### Quality Metrics

- **Search Relevance**: NDCG@10 > 0.8 for HR domain queries
- **Snippet Quality**: Manual evaluation > 90% accuracy for HR content
- **Method Coverage**: All 5 search methods operational with HR data
- **Error Rate**: < 1% of requests

### HR Domain-Specific Metrics

- **Section Filtering Accuracy**: > 95% correct section path filtering in `mock_hr` collection
- **Article Type Classification**: > 98% accuracy in distinguishing sections vs articles
- **Hierarchy Navigation**: Correct breadcrumb generation in 100% of results
- **Collection Accuracy**: All HR queries correctly routed to `mock_hr` Qdrant collection

## Risk Mitigation

### Technical Risks

1. **Qdrant Cloud Performance**: Implement caching and query optimization
2. **Model Loading Time**: Use model caching and warm-up procedures
3. **Data Synchronization**: Implement eventual consistency with conflict resolution for HR data
4. **Cloud Connectivity**: Implement retry logic and failover strategies
5. **Pagination Performance**: Monitor page sizes and optimize queries to prevent timeouts

### Operational Risks

1. **Deployment Complexity**: Use containerization and infrastructure as code
2. **Cost Management**: Monitor embedding generation costs and optimize batch processing
3. **Scalability**: Design for horizontal scaling from day one
4. **Cloud Vendor Lock-in**: Design abstraction layer for potential migration

## Next Steps

1. **Week 1**: Complete infrastructure setup with Qdrant Cloud and HR data sync
2. **Week 2**: Implement and test individual search methods with HR content
3. **Week 3**: Build hybrid search and advanced features for HR domain
4. **Week 4**: Integration testing and production deployment

This implementation will provide a robust, scalable search solution specifically designed for HR knowledge base content while maintaining compatibility with the existing FalkorDB graph structure and supporting multiple knowledge domains through dynamic collection naming.
