{"name": "internal-kb-tree-v2", "version": "0.1.0", "private": true, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0", "npm": "please-use-pnpm", "yarn": "please-use-pnpm"}, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "preinstall": "npx only-allow pnpm"}, "dependencies": {"@google/genai": "^1.5.1", "@hookform/resolvers": "^3.9.1", "@qdrant/js-client-rest": "^1.14.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "3.6.0", "embla-carousel-react": "8.5.1", "falkordb": "^6.2.7", "httpx": "^3.0.1", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "^0.4.4", "node-fetch": "^3.3.2", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "voyageai": "^0.0.4", "zod": "^3.24.1"}, "devDependencies": {"@types/dotenv": "^8.2.3", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^16.5.0", "eslint": "^9.29.0", "eslint-config-next": "15.3.4", "only-allow": "^1.2.1", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5"}}