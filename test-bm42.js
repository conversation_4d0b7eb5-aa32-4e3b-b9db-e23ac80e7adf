// Simple test script for BM42 API
async function testBM42() {
  console.log('Testing BM42 API directly...');
  
  try {
    const response = await fetch('https://fastembed-service-501978788717.us-central1.run.app/embed', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        texts: ['hello world', 'test connection']
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`BM42 API error (${response.status}): ${errorText}`);
    }

    const data = await response.json();
    console.log('✅ BM42 API test successful!');
    console.log('Response structure:', JSON.stringify(data, null, 2));
    
    if (data.vectors && Array.isArray(data.vectors)) {
      console.log(`Received ${data.vectors.length} vectors`);
      data.vectors.forEach((vector, i) => {
        console.log(`Vector ${i}: ${vector.indices?.length || 0} indices, ${vector.values?.length || 0} values`);
      });
    }
    
    return true;
  } catch (error) {
    console.error('❌ BM42 API test failed:', error);
    return false;
  }
}

testBM42().then(result => {
  process.exit(result ? 0 : 1);
});