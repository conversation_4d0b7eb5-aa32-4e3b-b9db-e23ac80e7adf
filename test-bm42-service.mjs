// Test the updated splade-service with BM42
import { spladeService, testSpladeService, generateSingleSpladeVector } from './lib/advanced-search/splade-service.js';

async function testBM42Service() {
  console.log('🧪 Testing BM42 Service Integration...');
  console.log('=====================================\n');

  // Test 1: Service status
  console.log('1️⃣ Testing service status...');
  const status = spladeService.getStatus();
  console.log('   Status:', JSON.stringify(status, null, 2));
  console.log('   ✅ Service configured\n');

  // Test 2: Connection test
  console.log('2️⃣ Testing connectivity...');
  try {
    const connected = await testSpladeService();
    console.log(`   Connection result: ${connected ? '✅ Connected' : '❌ Failed'}\n`);
  } catch (error) {
    console.error('   ❌ Connection test error:', error.message, '\n');
  }

  // Test 3: Single vector generation
  console.log('3️⃣ Testing single vector generation...');
  try {
    const vector = await generateSingleSpladeVector('hello world test');
    console.log(`   Generated vector: ${vector.indices.length} indices, ${vector.values.length} values`);
    console.log('   Sample indices:', vector.indices.slice(0, 5));
    console.log('   Sample values:', vector.values.slice(0, 5));
    console.log('   ✅ Single vector generation successful\n');
  } catch (error) {
    console.error('   ❌ Single vector error:', error.message, '\n');
  }

  // Test 4: Batch vector generation
  console.log('4️⃣ Testing batch vector generation...');
  try {
    const texts = [
      'employee onboarding process',
      'performance review guidelines', 
      'remote work policy'
    ];
    const vectors = await spladeService.generateSparseVectors(texts);
    console.log(`   Generated ${vectors.length} vectors for ${texts.length} texts`);
    vectors.forEach((vector, i) => {
      console.log(`   Vector ${i}: ${vector.indices.length} indices, ${vector.values.length} values`);
    });
    console.log('   ✅ Batch vector generation successful\n');
  } catch (error) {
    console.error('   ❌ Batch vector error:', error.message, '\n');
  }

  console.log('🎉 BM42 Service testing completed!');
}

testBM42Service().catch(console.error);